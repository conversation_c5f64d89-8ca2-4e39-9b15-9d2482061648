{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.3.6", "@faker-js/faker": "^8.4.1", "@microsoft/signalr": "^8.0.0", "@tanstack/react-query": "^5.32.0", "@tanstack/react-query-devtools": "^5.32.0", "antd": "^5.16.5", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "decimal.js": "^10.4.3", "i18next": "^24.2.2", "localforage": "^1.10.0", "lodash": "^4.17.21", "numbro": "^2.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.0", "react-icons": "^5.1.0", "react-router-dom": "^6.23.0", "react-to-print": "^2.15.1", "tailwindcss": "^3.4.10", "uuid": "^11.0.3", "xlsx": "^0.18.5", "zustand": "^4.5.2"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.13", "@types/node": "^20.12.7", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "7.0.0", "@typescript-eslint/parser": "7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "3.1.0", "prettier-eslint": "16.1.2", "sass": "^1.75.0", "typescript": "^5.2.2", "vite": "^5.2.0"}}