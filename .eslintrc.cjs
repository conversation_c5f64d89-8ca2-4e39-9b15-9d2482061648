module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  ignorePatterns: ['dist', '*.cjs', '*.config.ts'],
  parser: '@typescript-eslint/parser',
  extends: [
    'eslint:recommended',
    'plugin:react-hooks/recommended',
    'airbnb',
    'airbnb/hooks',
    'airbnb-typescript',
    'plugin:react/jsx-runtime',
    'plugin:prettier/recommended',
  ],
  parserOptions: {
    parser: '@typescript-eslint/parser',
    project: 'tsconfig.json',
  },
  plugins: ['react-refresh', 'import', '@typescript-eslint'],
  rules: {
    'react/no-unused-prop-types': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/require-default-props': [
      2,
      {
        forbidDefaultForRequired: true,
        ignoreFunctionalComponents: true,
      },
    ],
    'react/function-component-definition': [
      2,
      {
        namedComponents: ['function-expression', 'arrow-function'],
        unnamedComponents: ['function-expression'],
      },
    ],
    'import/no-unresolved': 'error',
    'linebreak-style': ['off', 'windows'],
    'no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
      },
    ],
    'no-empty-pattern': ['off'],
  },

  settings: {
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: ['./tsconfig.json'],
      },
    },
  },
};
