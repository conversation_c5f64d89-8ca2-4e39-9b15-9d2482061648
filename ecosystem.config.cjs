/** Paste the run script
 * pm2 start ecosystem.config.cjs --only UpClientDEV-3042 --attach
 */

const path = require('path');
const vitePath = path.normalize('./node_modules/vite/bin/vite.js');
const interpreterPath = path.normalize('/home/<USER>/.nvm/versions/node/v18.20.2/bin/node'); // -- ubuntu
// const interpreterPath = path.normalize('C:\\Program Files\\nodejs\\node.exe'); // -- windows

module.exports = {
  apps: [
    {
      name: 'UpClientDEV-3042',
      interpreter: interpreterPath,
      script: vitePath,
      args: '--host',
    },
  ],
};
