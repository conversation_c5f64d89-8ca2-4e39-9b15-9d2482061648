// libs
import { Skeleton } from 'antd';
import { Routes, Route } from 'react-router-dom';

interface ILoadingRoutesProps {}

const LoadingRoutes: React.FunctionComponent<ILoadingRoutesProps> = (props) => {
  // props
  const {} = props || {};

  return (
    <Routes>
      <Route
        path='*'
        element={
          <div style={{ border: '2px solid white' }}>
            <Skeleton active />
          </div>
        }
      />
    </Routes>
  );
};

export default LoadingRoutes;
