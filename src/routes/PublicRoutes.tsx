// libs
import { lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

const Login = lazy(() => import('../pages/auth/Login'));
const PublicLayout = lazy(() => import('../layouts/PublicLayout'));

interface IPublicRoutesProps {}
const PublicRoutes: React.FunctionComponent<IPublicRoutesProps> = (props) => {
  // props
  const {} = props || {};

  return (
    <Routes>
      <Route
        index
        element={<Navigate to='public' />}
      />
      <Route
        path='public'
        element={<PublicLayout />}
      >
        <Route
          index
          element={<Login />}
        />
      </Route>
      <Route
        path='public/*'
        element={<Navigate to='/public' />}
      />
      <Route
        path='*'
        element={<Navigate to='/public' />}
      />
    </Routes>
  );
};

export default PublicRoutes;
