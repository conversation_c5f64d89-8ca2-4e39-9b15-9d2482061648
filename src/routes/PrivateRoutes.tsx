import { lazy } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

const PrivateLayout = lazy(() => import('../layouts/PrivateLayout'));
const Overview = lazy(() => import('../pages/overview/Overview'));
const Deposit = lazy(() => import('../pages/deposit/Deposit'));
const Withdraw = lazy(() => import('../pages/withdraw/Withdraw'));
const DiOrder = lazy(() => import('../pages/di-order/DiOrder'));
const Ledger = lazy(() => import('../pages/ledger/Ledger'));
const Wallet = lazy(() => import('../pages/wallet/Wallet'));
const Record = lazy(() => import('../pages/record/Record'));
const SubAccount = lazy(() => import('../pages/subAccount/SubAccount'));
const User = lazy(() => import('../pages/user/User'));
const IpOverview = lazy(() => import('../pages/ip_overview/IpOverview'));

interface IPrivateRoutesProps {}
const PrivateRoutes: React.FunctionComponent<IPrivateRoutesProps> = (props) => {
  // props
  const {} = props || {};

  return (
    <Routes>
      <Route
        path='private'
        element={<PrivateLayout />}
      >
        <Route
          index
          element={<Overview />}
        />
        <Route
          path='deposit'
          element={<Deposit />}
        />
        <Route
          path='withdraw'
          element={<Withdraw />}
        />
        <Route
          path='di-order'
          element={<DiOrder />}
        />
        <Route
          path='ledger'
          element={<Ledger />}
        />
        <Route
          path='record'
          element={<Record />}
        />
        <Route
          path='wallet'
          element={<Wallet />}
        />
        <Route
          path='subAccount'
          element={<SubAccount />}
        />
        <Route
          path='ip'
          element={<IpOverview />}
        />
        <Route
          path='user'
          element={<User />}
        />
      </Route>

      <Route
        path='*'
        element={<Navigate to='/private' />}
      />
    </Routes>
  );
};

export default PrivateRoutes;
