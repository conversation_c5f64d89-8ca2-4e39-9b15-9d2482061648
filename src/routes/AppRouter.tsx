// libs
import { useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash';
import { HashRouter, Route, Routes } from 'react-router-dom';

// apis
import { useInfo } from '@/api';

// store
import { useUserStore } from '@/store/useUserStore';

// routes
import PrivateRoutes from '@/routes/PrivateRoutes';
import LoadingRoutes from './LoadingRoutes';
import PublicRoutes from './PublicRoutes';

interface IAppRouterProps {}
const AppRouter: React.FunctionComponent<IAppRouterProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [enableQuery, setEnableQuery] = useState(false);
  const [routes, setRoutes] = useState(<LoadingRoutes />);

  // hooks
  const { loginRes, info, isBackFilledLoginRes } = useUserStore();

  // query
  useInfo({
    params: { id: loginRes?.id },
    enabled: enableQuery,
    blockNotify: true,
  });

  // Handle change Routes
  const changeD = useMemo(
    () =>
      debounce(() => {
        if (loginRes && info) {
          setRoutes(<PrivateRoutes />);
        } else if (!loginRes && !info && isBackFilledLoginRes) {
          setRoutes(<PublicRoutes />);
        } else {
          setRoutes(<LoadingRoutes />);
        }
      }, 400),
    [info, isBackFilledLoginRes, loginRes],
  );

  // === init === //
  // enable query
  useEffect(() => {
    setTimeout(() => {
      setEnableQuery(true);
    }, 100);
  }, [setEnableQuery]);

  // Set routes
  useEffect(() => {
    const isTestRoutes = false;
    if (import.meta.env.DEV && isTestRoutes) {
      setRoutes(<PrivateRoutes />);
      return () => {};
    }

    changeD();
    return () => {
      changeD.cancel();
    };
  }, [changeD]);

  return (
    <HashRouter>
      <Routes>
        <Route
          path='/*'
          element={routes}
        />
      </Routes>
    </HashRouter>
  );
};

export default AppRouter;
