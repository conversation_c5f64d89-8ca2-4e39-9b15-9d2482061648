// libs
import { useState } from 'react';

type TableStatesProps = {
  sizeInit?: number;
  currentInit?: number;
};
const useTableStates = (useProps: TableStatesProps) => {
  // props
  const { sizeInit, currentInit } = useProps;

  // states
  const [pageSize, setPageSize] = useState(sizeInit || 10);
  const [currentPage, setCurrentPage] = useState(currentInit || 1);

  return { pageSize, setPageSize, currentPage, setCurrentPage };
};

export default useTableStates;
export type { TableStatesProps };
