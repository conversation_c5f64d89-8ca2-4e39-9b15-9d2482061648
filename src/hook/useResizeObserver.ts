// libs
import { useEffect, useRef } from 'react';

const useResizeObserver = <T extends Element>(callback: (entry: ResizeObserverEntry) => void) => {
  const elementRef = useRef<T>(null);

  useEffect(() => {
    const elementCurrent = elementRef.current;
    if (!elementCurrent) return () => {};
    const ro = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.target === elementCurrent) {
          callback(entry);
        }
      });
    });
    ro.observe(elementCurrent);

    return () => {
      ro.unobserve(elementCurrent);
    };
  }, [callback]);

  return elementRef;
};

export default useResizeObserver;
