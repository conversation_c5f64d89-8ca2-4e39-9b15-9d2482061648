// libs
import { AxiosError, InternalAxiosRequestConfig } from 'axios';
import {
  QueryKey,
  UseQueryOptions,
  QueryClient,
  useQuery,
  UseMutationOptions,
  useMutation,
} from '@tanstack/react-query';
import { useState } from 'react';

// utils
import { logInfo, logWarn } from '@/utils';

// store
import { type NotifyBaseOptions, useNotifyStore } from '@/store';

// hooks
import useLogout from './useLogout';

const withTestLog = false;

// ### query
type TestQueryProps<TDATA = unknown, TPARAMS = unknown, TERROR = AxiosError<RequestError>> = {
  isTest?: boolean;
  onTest?: (params?: TPARAMS) => Promise<TDATA>;
  isTestError?: boolean;
  delay?: number;
  onQuery?: (params?: TPARAMS) => void;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  params?: TPARAMS;
  qf: (params?: TPARAMS) => Promise<TDATA>;
  blockNotify?: boolean;
  skipLogger?: boolean;
};

const useTestQuery = <
  TDATA = unknown,
  TPARAMS = unknown,
  TERROR = AxiosError<RequestError>,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UseQueryOptions<TDATA, TERROR, TPARAMS, TQueryKey> & TestQueryProps<TDATA, TPARAMS, TERROR>,
  client?: QueryClient,
) => {
  // status
  const {
    qf,
    params,
    isTest,
    onTest,
    isTestError,
    delay,
    onQuery,
    onSuccess,
    onError,
    onSettled,
    blockNotify,
    queryKey,
    skipLogger,
    ...originOptions
  } = options;

  // states
  const [isError, setIsError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // hooks
  const { pushBEQ } = useNotifyStore();
  const { handleClean } = useLogout();

  const { data: whyIsParam, ...result } = useQuery<TDATA, TERROR, TPARAMS, TQueryKey>(
    {
      ...originOptions,
      queryKey,
      queryFn: () => {
        if (onQuery) onQuery(params);
        const request = new Promise<TDATA>((resolve, reject) => {
          if (process.env.NODE_ENV === 'development' && isTestError) {
            const makeError: AxiosError<RequestError> = {
              isAxiosError: false,
              toJSON: (): object => ({}),
              name: '',
              message: '',
              response: {
                data: {
                  errorCode: -1,
                  errors: {},
                  status: -1,
                  title: 'Test query error',
                  type: '',
                },
                status: -1,
                statusText: '',
                headers: {},
                config: {} as InternalAxiosRequestConfig<any>,
              },
            };
            reject(makeError);
            return;
          }

          if (process.env.NODE_ENV === 'development' && isTest) {
            if (onTest)
              setTimeout(() => {
                resolve(onTest(params));
              }, delay || 0);
            return;
          }
          resolve(qf(params));
        })
          .then((value) => {
            setIsSuccess(true);
            setIsError(false);
            if (withTestLog && process.env.NODE_ENV === 'development' && !skipLogger)
              logInfo({ title: `Query test success, key: ${queryKey}`, value });
            if (onSuccess) onSuccess(value, params);

            return value;
          })
          .catch((error) => {
            setIsSuccess(false);
            setIsError(true);
            const mikeError = error as AxiosError<RequestError>;
            if (process.env.NODE_ENV === 'development')
              logWarn({ title: 'Query test error', error: mikeError.response?.data });
            // notify
            if (!blockNotify && mikeError.response?.status && [400].includes(mikeError.response.status))
              pushBEQ([
                {
                  title: 'UXM store',
                  des: mikeError.response?.data?.title,
                },
              ]);

            if (
              mikeError.response?.data?.errors &&
              mikeError.response?.status &&
              [400].includes(mikeError.response.status)
            ) {
              Object.values(mikeError.response.data.errors).forEach((eachError) => {
                pushBEQ([
                  {
                    title: 'UXM warning !',
                    des: eachError.join(''),
                  },
                ]);
              });
            }

            // auth
            if (mikeError.response?.status === 401) {
              logWarn('Test request failed due to authentication');
              handleClean();
            }
            if (onError) onError(error);
            return error;
          })
          .finally(() => {
            if (onSettled) onSettled();
          });

        return request;
      },
    },
    client,
  );
  const data = (whyIsParam as TDATA) ?? undefined;

  return { ...result, data, isSuccess, isError };
};

type UseTestQueryProps<
  Other = unknown,
  TPARAMS = unknown,
  TDATA = unknown,
  TERROR = AxiosError<RequestError>,
> = Other & {
  enabled?: boolean;
  isTest?: boolean;
  params?: TPARAMS;
  isTestError?: boolean;
  onQuery?: (params?: TPARAMS) => void;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
};

// mutation
type TestMutationProps<TDATA = unknown, TPARAMS = unknown, TERROR = AxiosError<RequestError>> = {
  isTest?: boolean;
  isTestError?: boolean;
  onTest?: (params: TPARAMS) => Promise<TDATA>;
  delay?: number;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
  successNotify?: NotifyBaseOptions;
  skipLogger?: boolean;
};

const useTestMutation = <TDATA = unknown, TPARAMS = void, TERROR = AxiosError<RequestError>, TContext = unknown>(
  options: UseMutationOptions<TDATA, TERROR, TPARAMS, TContext> & TestMutationProps<TDATA, TPARAMS, TERROR>,
  queryClient?: QueryClient,
) => {
  const {
    isTest,
    isTestError,
    onTest,
    delay,
    mutationFn,
    onSuccess,
    onError,
    blockNotify,
    successNotify,
    skipLogger,
    ...originOptions
  } = options;

  // hooks
  const { pushBEQ, pushBSQ } = useNotifyStore();
  const { handleClean } = useLogout();

  const newConfig: UseMutationOptions<TDATA, TERROR, TPARAMS, TContext> = {
    ...originOptions,
    mutationFn: (props) => {
      return new Promise((resolve, reject) => {
        if (isTestError && process.env.NODE_ENV === 'development') {
          const makeError: AxiosError<RequestError> = {
            isAxiosError: false,
            toJSON: (): object => ({}),
            name: '',
            message: '',
            response: {
              data: {
                errorCode: -1,
                errors: {},
                status: -1,
                title: 'Test mutation error',
                type: '',
              },
              status: -1,
              statusText: '',
              headers: {},
              config: {} as InternalAxiosRequestConfig<any>,
            },
          };
          reject(makeError);

          return;
        }
        if (isTest && process.env.NODE_ENV === 'development' && onTest) {
          setTimeout(() => {
            resolve(onTest(props));
          }, delay || 0);

          return;
        }
        if (mutationFn) resolve(mutationFn(props));
      });
    },
    onSuccess: (data, parms, context) => {
      if (withTestLog && process.env.NODE_ENV === 'development' && !skipLogger) {
        logWarn({ title: 'On test mutation success !', data, parms, context });
      }

      if (successNotify) pushBSQ([successNotify]);
      if (onSuccess) onSuccess(data, parms, context);
    },
    onError: (error, params, context) => {
      if (process.env.NODE_ENV === 'development') {
        logWarn({ title: 'On test mutation error, error:', error });
      }

      const mikeError = error as AxiosError<RequestError>;
      if (!blockNotify) {
        if (!mikeError.response) {
          pushBEQ([
            {
              title: 'UXM',
              des: '網路連線異常',
            },
          ]);
          handleClean();

          return;
        }

        if (mikeError.response?.data && mikeError.response?.status && [400].includes(mikeError.response.status))
          pushBEQ([
            {
              title: 'UXM warning!',
              des: mikeError.response?.data?.title,
            },
          ]);

        if (
          mikeError.response?.data?.errors &&
          mikeError.response?.status &&
          [400].includes(mikeError.response.status)
        ) {
          Object.values(mikeError.response.data.errors).forEach((eachError) => {
            pushBEQ([
              {
                title: 'UXM warning !',
                des: eachError.join(''),
              },
            ]);
          });
        }
      }

      // auth
      if (mikeError.response?.status === 401) {
        logWarn('Test request failed due to authentication');
        handleClean();
      }
      // all err 403
      if (mikeError.response?.status === 403) {
        pushBEQ([{ title: 'UXM Client', des: '您没有权限执行此操作' }]);
      }

      if (onError) onError(error, params, context);
    },
  };

  return useMutation<TDATA, TERROR, TPARAMS, TContext>(newConfig, queryClient);
};

// 這個用作於使用 test mutation 建立 hook時快速設定 props
type UseTestMutationProps<TDATA = unknown, TPARAMS = unknown, Other = unknown, TERROR = AxiosError<RequestError>> = {
  isTest?: boolean;
  onTest?: (params: TPARAMS) => Promise<TDATA>;
  delay?: number;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
  successNotify?: NotifyBaseOptions;
} & Other;
export type { UseTestQueryProps, UseTestMutationProps };
export { useTestQuery, useTestMutation };
