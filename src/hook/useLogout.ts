// libs
import { useCallback } from 'react';

// utils
import { forage, logWarn } from '@/utils';

// store
import { useUserStore } from '@/store';

const useLogout = () => {
  // hooks
  const { reset: resetUser, setIsLoading } = useUserStore();

  const handleClean = useCallback(() => {
    setIsLoading(true);
    if (typeof window !== 'undefined' && 'caches' in window) {
      caches
        .keys()
        .then((cacheNames) => {
          cacheNames.forEach((cacheName) => {
            caches.delete(cacheName);
          });
        })
        .catch((error) => {
          logWarn({ Title: 'Error clearing cache', error });
        });
    }
    forage().clear(() => {
      localStorage.clear();
      resetUser();
      setIsLoading(false);
    });
  }, [resetUser, setIsLoading]);

  return { handleClean };
};

export default useLogout;
