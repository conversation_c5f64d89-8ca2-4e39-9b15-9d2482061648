import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { HttpError, HubConnection, HubConnectionBuilder, HubConnectionState } from '@microsoft/signalr';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from 'react-i18next';
import { logInfo, logWarn } from '@/utils';
import queryKeys from '@/utils/queryKeys';
import { useNotifyStore, useOfficeHubStore, useUserStore } from '@/store';
import useLogout from './useLogout';
import { dateFormator } from './useDateTool';
import { MemberOrderNotifyDto, MerchantBalanceNotifyDto, MerchantTransDto } from './type';

const url = `${import.meta.env.VITE_HUBS_ROOT}/merchantHub`;
const { Connected, Disconnected } = HubConnectionState;

type UseMerchantHubProps = {
  onMON?: (message: MemberOrderNotifyDto) => void;
  onMCON?: (message: MerchantTransDto) => void;
  onMCBL?: (message: MerchantBalanceNotifyDto) => void;
};

const useMerchantHub = (useProps: UseMerchantHubProps) => {
  // props
  const { onMON, onMCON, onMCBL } = useProps;

  // refs
  const isAlreadyBuild = useRef(false);
  const autoReconnectBlock = useRef(false);

  // hooks
  const queryClient = useQueryClient();
  const { loginRes, setIsConnected } = useUserStore();
  const { setHubConnection, hubConnection, setHubConnectionState, onMONCalls, onMCONCalls, onMCBLCalls } =
    useOfficeHubStore();
  const { state } = hubConnection || {};
  const { pushBEQ, pushBSQ, pushHQ, hintQue } = useNotifyStore();
  const { handleClean } = useLogout();
  const { t } = useTranslation('useMerchantHub');

  // === init === //
  useEffect(() => {
    if (state !== undefined) setHubConnectionState(state);
  }, [setHubConnectionState, state]);

  useEffect(() => {
    const handleLogoutDisconnect = async () => {
      if (hubConnection && hubConnection.state === Connected) {
        try {
          await hubConnection.stop();
          logInfo('Disconnected successfully');
        } catch (error) {
          logWarn({ Title: 'Error disconnecting from hub:', error });
        }
      }
      setHubConnection(null);
      setIsConnected(false);

      // clear notify data when logout
      hintQue.length = 0;
      logInfo('Cleared hintQue successfully');
    };

    if (!loginRes) {
      handleLogoutDisconnect();
    }
  }, [loginRes, hubConnection, setHubConnection, setIsConnected, hintQue]);

  useEffect(() => {
    const handleReconnect = (connection: HubConnection | null, tryTime = 0) => {
      if (!connection || tryTime > 10 || connection.state === Connected) return;

      connection
        .start()
        .then(() => {
          logInfo('MerchantHub connected successfully.');
          setIsConnected(true);
          setHubConnectionState(Connected);
        })
        .catch((error: HttpError) => {
          logWarn({ Title: 'MerchantHub connect failed', error });
          if (error.message.includes('401')) {
            handleClean();
          } else {
            setTimeout(() => handleReconnect(connection, tryTime + 1), 2000);
          }
        });
    };

    if (hubConnection?.state === Disconnected && !autoReconnectBlock.current) {
      handleReconnect(hubConnection);
      autoReconnectBlock.current = false;
    }
  }, [handleClean, hubConnection, setHubConnectionState, setIsConnected]);

  // === Set events === //
  const alreadySetEventsID = useRef<string | null>(null);
  useEffect(() => {
    const alreadySetEventsIDCurrent = alreadySetEventsID.current;
    if (
      state !== Connected ||
      !hubConnection ||
      (alreadySetEventsIDCurrent && alreadySetEventsIDCurrent === hubConnection.connectionId)
    )
      return;
    // === off old event, avoid duplicate event calls ===
    hubConnection.off('MemberOrderNotify');
    hubConnection.off('MerchantOrderNotify');
    hubConnection.off('MerchantBalanceUpdateNotify');

    alreadySetEventsID.current = hubConnection.connectionId;

    // Set up event handlers
    hubConnection.on('MemberOrderNotify', (message: MemberOrderNotifyDto) => {
      const { to, merchantOrderId, requireAmount, merchantNumber, status, transactionType } = message;
      pushBSQ([
        {
          title: 'UXM Merchant',
          des: `${t('memberOrder')} \n ${t('merchantNumber')}: ${merchantNumber} ${t(
            'requireAmount',
          )}: ${requireAmount}`,
        },
      ]);
      pushHQ([
        {
          id: uuidv4(),
          title: transactionType === 1 ? t('memberDeposit') : t('memberWithdraw'),
          des: `${t('orderNumber')}: ${merchantOrderId} \n ${t('requireAmount')}: ${requireAmount} \n ${t(
            'to',
          )}: ${to}`,
          notifyType: 'MemberOrder',
          transactionType,
          status,
          createdAt: dayjs().format(dateFormator.accurate),
          isReaded: false,
        },
      ]);
      // auto refetch member order list page
      queryKeys.query.allOrderList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      if (onMON) onMON(message);
      Object.values(onMONCalls).forEach((call) => {
        call(message);
      });
    });

    hubConnection.on('MerchantOrderNotify', (message: MerchantTransDto) => {
      const { applicantName, order } = message;
      const { to, requireAmount, transactionType, status } = order;
      pushBSQ([
        {
          title: 'UXM Merchant',
          des: `${t('merchantOrder')} \n ${t(
            'applicantName',
          )}: ${applicantName} {t('requireAmount')}: ${requireAmount}`,
        },
      ]);
      pushHQ([
        {
          id: uuidv4(),
          title: transactionType === 1 ? t('transferIn') : t('transferOut'),
          des: `${t('requireAmount')}: ${requireAmount} \n ${t('to')}: ${to}`,
          notifyType: 'MerchantOrder',
          transactionType,
          status,
          createdAt: dayjs().format(dateFormator.accurate),
          isReaded: false,
        },
      ]);
      // auto refetch merchant order list page
      queryKeys.query.allMerchantOrderList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      if (onMCON) onMCON(message);
      Object.values(onMCONCalls).forEach((call) => {
        call(message);
      });
    });

    hubConnection.on('MerchantBalanceUpdateNotify', (message: MerchantBalanceNotifyDto) => {
      const { balance, lockedBalance } = message;
      pushBSQ([
        {
          title: 'UXM Merchant',
          des: `${t('availableBalance')}: ${balance} \n ${t('lockedBalance')}: ${lockedBalance}`,
        },
      ]);
      pushHQ([
        {
          id: uuidv4(),
          title: t('wallet'),
          des: `${t('availableBalance')}: ${balance} \n ${t('lockedBalance')}: ${lockedBalance}`,
          notifyType: 'MerchantBalanceUpdate',
          createdAt: dayjs().format(dateFormator.accurate),
          isReaded: false,
        },
      ]);
      queryKeys.query.allMerchantDetailBalance().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });
      if (onMCBL) onMCBL(message);
      Object.values(onMCBLCalls).forEach((call) => {
        call(message);
      });
    });
  }, [
    hubConnection,
    pushBEQ,
    pushBSQ,
    pushHQ,
    hintQue.length,
    onMON,
    onMONCalls,
    onMCON,
    onMCONCalls,
    onMCBL,
    onMCBLCalls,
    state,
    queryClient,
    t,
  ]);

  // === Build new connection when login occurs === //
  useEffect(() => {
    if (!loginRes || hubConnection || isAlreadyBuild.current) return;

    isAlreadyBuild.current = true;
    setTimeout(() => {
      isAlreadyBuild.current = false;
    }, 500);

    const newConnection = new HubConnectionBuilder()
      .withUrl(url, {
        accessTokenFactory: () => loginRes.token,
      })
      .build();

    newConnection.onclose = (e) => {
      logWarn({ Title: 'Hub onClose!!', e });
    };

    setHubConnection(newConnection);
    // eslint-disable-next-line consistent-return
    return () => {
      if ([Connected].includes(newConnection.state)) {
        newConnection
          .stop()
          .then(() => {
            logWarn('Connection disconnected!');
          })
          .catch((e) => logWarn({ Title: 'Hub stop error!!, error:', e }));
      }
    };
  }, [loginRes, hubConnection, setHubConnection]);
};

export default useMerchantHub;
export type { MemberOrderNotifyDto };
