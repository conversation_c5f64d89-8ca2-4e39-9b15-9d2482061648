// libs
import { HubConnection, HubConnectionState } from '@microsoft/signalr';
import { create } from 'zustand';

// hooks
import { MemberOrderNotifyDto, MerchantBalanceNotifyDto, MerchantTransDto } from '@/hook/type';

type OfficeHubStoreInit = {
  hubConnection: null | HubConnection;
  connectionState: null | HubConnectionState;
  onMONCalls: Record<string, (signal: MemberOrderNotifyDto) => void>;
  onMCONCalls: Record<string, (signal: MerchantTransDto) => void>;
  onMCBLCalls: Record<string, (signal: MerchantBalanceNotifyDto) => void>;
};

interface IOfficeHubStore extends OfficeHubStoreInit {
  setHubConnection: (hubConnection: OfficeHubStoreInit['hubConnection']) => void;
  setHubConnectionState: (connectionState: OfficeHubStoreInit['connectionState']) => void;
  addMONCalls: (key: string, call: (signal: MemberOrderNotifyDto) => void) => void;
  rmMONCalls: (key: string) => void;
  addMCONCalls: (key: string, call: (signal: MerchantTransDto) => void) => void;
  rmMCONCalls: (key: string) => void;
  addMCBLCalls: (key: string, call: (signal: MerchantBalanceNotifyDto) => void) => void;
  rmMCBLCalls: (key: string) => void;
}

const init: OfficeHubStoreInit = {
  hubConnection: null,
  connectionState: null,
  onMONCalls: {},
  onMCONCalls: {},
  onMCBLCalls: {},
};

const useOfficeHubStore = create<IOfficeHubStore>((set) => {
  return {
    ...init,
    setHubConnection: (hubConnection) => set({ hubConnection }),
    setHubConnectionState: (connectionState) => set({ connectionState }),
    addMONCalls: (key, call) => {
      set((preStates) => {
        const pureStates = { ...preStates };
        pureStates.onMONCalls[key] = call;
        return pureStates;
      });
    },
    rmMONCalls: (key) => {
      set((preStates) => {
        const updatedCalls = { ...preStates.onMONCalls };
        delete updatedCalls[key];
        return { ...preStates, onMONCalls: updatedCalls };
      });
    },
    addMCONCalls: (key, call) => {
      set((preStates) => {
        const pureStates = { ...preStates };
        pureStates.onMCONCalls[key] = call;
        return pureStates;
      });
    },
    rmMCONCalls: (key) => {
      set((preStates) => {
        const updatedCalls = { ...preStates.onMCONCalls };
        delete updatedCalls[key];
        return { ...preStates, onMCONCalls: updatedCalls };
      });
    },
    addMCBLCalls: (key, call) => {
      set((preStates) => {
        const pureStates = { ...preStates };
        pureStates.onMCBLCalls[key] = call;
        return pureStates;
      });
    },
    rmMCBLCalls: (key) => {
      set((preStates) => {
        const updatedCalls = { ...preStates.onMCBLCalls };
        delete updatedCalls[key];
        return { ...preStates, onMCBLCalls: updatedCalls };
      });
    },
  };
});

export { useOfficeHubStore };
export type { OfficeHubStoreInit, IOfficeHubStore };
