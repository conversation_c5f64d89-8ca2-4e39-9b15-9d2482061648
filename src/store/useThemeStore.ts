/* eslint-disable import/prefer-default-export */
// libs
import { create } from 'zustand';

type ThemeStoreInit = {
  isWhite: boolean;
};

interface ThemeStoreOptions extends ThemeStoreInit {
  setIsWhite: (isWhite?: boolean) => void;
}

const init: ThemeStoreInit = {
  isWhite: false,
};

const useThemeStore = create<ThemeStoreOptions>((set) => {
  return {
    ...init,
    setIsWhite: (isWhite) => {
      if (isWhite === undefined) {
        set((origin) => {
          const pureState = { ...origin };
          pureState.isWhite = !origin.isWhite;
          return pureState;
        });
      } else {
        set({ isWhite });
      }
    },
  };
});

export { useThemeStore };
