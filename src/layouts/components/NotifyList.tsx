// libs
import { useMemo, useState, useEffect } from 'react';
import { Divider, Empty } from 'antd';

// store
import { useNotifyStore } from '@/store';

// layouts
import styles from './notifyList.module.scss';
import NotifyItem from './NotifyItem';

const NotifyList: React.FC = () => {
  // states
  const [alreadyShowID, setAlreadyShowID] = useState<Array<string>>([]);

  // hooks
  const { hintQue, readHQ } = useNotifyStore();

  // compute
  const unreadNotifications = useMemo(() => {
    return hintQue.filter((item) => !item.isReaded);
  }, [hintQue]);

  const isNoUnread = useMemo(() => {
    return unreadNotifications.length === 0;
  }, [unreadNotifications]);

  useEffect(() => {
    hintQue.forEach((newsItem) => {
      setAlreadyShowID((prevIDs) => {
        const newIDs = Array.from(new Set([...prevIDs, newsItem.id]));

        return newIDs;
      });
    });
  }, [hintQue]);

  return (
    <div className={`flex w-96 flex-col overflow-x-hidden p-2 ${styles['list-container']}`}>
      <Empty className={`overflow-y-hidden transition-all duration-300 ${isNoUnread ? 'max-h-[50vh]' : 'max-h-0'}`} />
      {unreadNotifications.map((newsItem, index) => {
        const { id, isReaded, title, des, createdAt, transactionType, status, notifyType } = newsItem;
        const notLast = index + 1 < unreadNotifications.length;
        return (
          <div key={id}>
            <NotifyItem
              {...{
                id,
                isReaded,
                title,
                des,
                createdAt,
                transactionType,
                notLast,
                readHQ,
                alreadyShowID,
                status,
                notifyType,
              }}
            />
            {notLast && (
              <Divider
                variant='dashed'
                orientation='center'
                style={{ borderWidth: '1px' }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default NotifyList;
