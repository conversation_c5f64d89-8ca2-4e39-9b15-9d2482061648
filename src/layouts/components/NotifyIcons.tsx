// libs
import { DollarOutlined, SwapOutlined, WalletOutlined } from '@ant-design/icons';

interface NotifyIconProps {
  notifyType: string | undefined;
  transactionType?: number;
}

const NotifyIcons: React.FunctionComponent<NotifyIconProps> = (props) => {
  // props
  const { notifyType, transactionType } = props || {};

  if (notifyType === 'MemberOrder' || notifyType === 'MerchantOrder') {
    return transactionType === 1 ? (
      <DollarOutlined style={{ fontSize: '20px', color: '#52c41a' }} />
    ) : (
      <SwapOutlined style={{ fontSize: '20px', color: '#FB4141' }} />
    );
  }

  if (notifyType === 'MerchantBalanceUpdate') {
    return <WalletOutlined style={{ fontSize: '20px', color: '#1890ff' }} />;
  }

  return null;
};

export default NotifyIcons;
