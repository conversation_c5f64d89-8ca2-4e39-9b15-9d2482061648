// libs
import { <PERSON>Outlined, MoonOutlined, BellFilled } from '@ant-design/icons';
import { Badge, Button, Divider, Flex, Space, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// apis
import { useMcDetails } from '@/api';

// assets
import logoLight from '@/assets/medium-logo-light.png';
import logoDark from '@/assets/medium-logo-dark.png';

// components
import DropdownAlpha, { DropdownLocale } from '@/components/DropdownAlpha';

// hooks
import useLogout from '@/hook/useLogout';

// utils
import { CryptoEnum } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';
import { useNotifyStore, useUserStore } from '@/store';

// layouts
import BalanceHeader from './BalanceHeader';
import NotifyList from './NotifyList';

interface IPrivateHeaderProps {}

const PrivateHeader: React.FunctionComponent<IPrivateHeaderProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { handleClean } = useLogout();
  const { t } = useTranslation('privateHeader');
  const { isWhite, setIsWhite } = useThemeStore();
  const { info } = useUserStore();
  const { data: walletMerchant, isPending: isLoad } = useMcDetails({});
  const { hintQue } = useNotifyStore();
  const unreadCount = hintQue.filter((hint) => !hint.isReaded).length;

  return (
    <>
      <Space
        align='center'
        size='large'
      >
        <Link
          to='/'
          className='text-nowrap flex h-9 items-center'
        >
          <img
            src={isWhite ? logoLight : logoDark}
            alt='logo'
            className='h-full'
          />
        </Link>
        <div>
          {t('merchant')}：{walletMerchant?.merchantNumber}
        </div>
        <Flex
          gap={5}
          align='center'
        >
          <Flex
            gap={5}
            align='center'
          >
            <BalanceHeader
              type={CryptoEnum.TRC20_USDT}
              balance={walletMerchant?.wallet?.balance}
              lockedBalance={walletMerchant?.wallet?.lockedBalance}
              loading={isLoad}
            />
          </Flex>
        </Flex>
      </Space>

      <Space>
        <Link to='user'>
          <Button>
            {t('welcome')} {info?.nickName}
          </Button>
        </Link>
        <Button
          type='text'
          onClick={() => handleClean()}
        >
          {t('signOut')}
        </Button>
        <Divider type='vertical' />
        <Space size='small'>
          <DropdownAlpha
            pannelMaxHeight='50vh'
            itemHeight='fit-content'
            noUnderLink
            withoutIcon
            buttonProps={{ type: 'text', shape: 'circle' }}
            items={[
              {
                key: '1',
                item: <NotifyList />,
              },
            ]}
          >
            <Tooltip title={t('notify')}>
              <Badge
                count={unreadCount}
                overflowCount={99}
                color='red'
                key='badge'
              >
                <BellFilled />
              </Badge>
            </Tooltip>
          </DropdownAlpha>
        </Space>
        <Divider type='vertical' />
        <Button
          type='text'
          icon={isWhite ? <SunOutlined /> : <MoonOutlined />}
          onClick={() => setIsWhite()}
        />
        <DropdownLocale />
      </Space>
    </>
  );
};

export default PrivateHeader;
