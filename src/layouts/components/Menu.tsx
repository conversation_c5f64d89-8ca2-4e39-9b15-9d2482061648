// libs
import { HtmlHTMLAttributes, useEffect, useRef, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Tabs, Typography } from 'antd';
import { TabsProps } from 'antd/lib';

type MenuOptions = {
  label: React.ReactNode;
  to: string;
  key: string;
};

interface IMenuProps extends Omit<TabsProps, 'items'> {
  prefix: string;
  items: Array<MenuOptions>;
  containerProps?: HtmlHTMLAttributes<HTMLDivElement>;
}

const Menu: React.FunctionComponent<IMenuProps> = (props) => {
  // props
  const { prefix, items, containerProps, ...tabProps } = props || {};

  // refs
  const tabsRef = useRef<HTMLDivElement>(null);

  // states
  const [containerMinWidth, setContainerMinWidth] = useState<number>();

  // compute
  const location = useLocation();
  const vaildPath = location.pathname.replace(prefix, '');
  const activeKey = vaildPath.split('/').at(1);

  const tabItems = items.reduce(
    (preItems, item) => {
      const { label, to, key } = item;
      return [
        ...preItems,
        {
          key,
          label: (
            <Link {...{ to }}>
              <Typography.Text>{label}</Typography.Text>
            </Link>
          ),
        },
      ];
    },
    [] as Array<{ key: string; label: React.ReactNode }>,
  );

  useEffect(() => {
    if (!tabsRef.current) return;
    setContainerMinWidth(tabsRef.current.scrollWidth + 5);
  }, [setContainerMinWidth]);

  return (
    <div
      style={{ minWidth: containerMinWidth }}
      {...containerProps}
    >
      <div ref={tabsRef}>
        <Tabs
          tabBarStyle={{ marginBottom: 0 }}
          items={tabItems}
          {...{ activeKey, ...tabProps }}
          tabPosition='right'
          className='mt-4 w-fit'
        />
      </div>
    </div>
  );
};

export default Menu;
