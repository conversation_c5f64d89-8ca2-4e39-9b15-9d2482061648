/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
// libs
import React from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import { Txt } from '@/components/TypographyMaster';
import TagStatusMcOrder from '@/components/TagAlpha/TagStatusMcOrder';
import { TagTxStatus } from '@/components/TagAlpha';

// layouts
import styles from './notifyList.module.scss';
import NotifyIcons from './NotifyIcons';

interface NotifyItemProps {
  id: string;
  isReaded: boolean;
  title: string;
  des: string;
  createdAt: string;
  transactionType?: number;
  readHQ: (id: string) => void;
  alreadyShowID: string[];
  status?: number;
  notifyType?: 'MemberOrder' | 'MerchantOrder' | 'MerchantBalanceUpdate';
}

const NotifyItem: React.FC<NotifyItemProps> = React.memo((props) => {
  // props
  const { id, isReaded, title, des, createdAt, transactionType, readHQ, alreadyShowID, status, notifyType } = props;

  // hooks
  const { t } = useTranslation('notifyItem');

  const icon = NotifyIcons({ notifyType, transactionType });

  const isAlreadyShown = alreadyShowID.includes(id);

  return (
    <div
      key={id}
      className={`${styles.item} ${!isReaded ? 'pb-2' : 'pb-0'} ${!isReaded ? '' : styles.hidden} ${
        isAlreadyShown ? styles.active : ''
      }`}
    >
      <header className='mb-2 flex items-center justify-between'>
        <div className='flex items-center space-x-2'>
          <div className='flex h-5 w-5 items-center justify-center'>{icon}</div>
          <h5 className='text-base leading-none'>{title}</h5>
        </div>
        <div>
          {notifyType === 'MemberOrder' || notifyType === 'MerchantOrder' ? (
            notifyType === 'MemberOrder' ? (
              <TagTxStatus {...{ status }} />
            ) : (
              <TagStatusMcOrder {...{ status }} />
            )
          ) : null}
        </div>
      </header>

      <main className='mb-2 mt-2 space-y-2'>
        {des.split('\n').map((line) => (
          <p key={line}>{line}</p>
        ))}
      </main>

      <footer className='mt-4 flex items-center justify-between'>
        <Button
          onClick={() => readHQ(id)}
          size='small'
          className='mr-2'
        >
          {t('Read')}
        </Button>
        <Txt type='secondary'>{createdAt}</Txt>
      </footer>
    </div>
  );
});

export default NotifyItem;
