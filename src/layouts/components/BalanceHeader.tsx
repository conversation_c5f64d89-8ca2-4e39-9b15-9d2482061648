// libs
import { <PERSON><PERSON><PERSON>, <PERSON>lex, Avatar, Divider, Skeleton } from 'antd';
import { useTranslation } from 'react-i18next';

// assets
import usdt from '@/assets/usdt.png';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { CryptoEnum, nTot } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';

interface IBalanceHeaderProps {
  type: CryptoEnum;
  balance: number | string | undefined;
  lockedBalance: number | string | undefined;
  loading: boolean;
}
const BalanceHeader: React.FunctionComponent<IBalanceHeaderProps> = (props) => {
  // props
  const { type, balance, lockedBalance, loading } = props || {};

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('balanceHeader');

  if (type === CryptoEnum.TRC20_USDT)
    return (
      <Tooltip title={t('tooltip')}>
        <Flex
          gap={10}
          align='center'
          className='mr-2 rounded-full pr-2'
          style={{ border: isWhite ? '1px solid #d9d9d9' : '1px solid #434343' }}
        >
          <Avatar
            src={usdt}
            size='small'
          />
          <Txt>{t('availableBalance')}:</Txt>
          {loading ? (
            <Skeleton.Input
              active
              style={{ width: 20, height: 10, marginBottom: 10 }}
            />
          ) : (
            <Txt>{nTot({ value: balance })}</Txt>
          )}
          <Divider
            type='vertical'
            className='top-0 m-0'
          />
          <Txt>{t('lockedBalance')}:</Txt>
          <Txt type='danger'>{nTot({ value: lockedBalance })}</Txt>
        </Flex>
      </Tooltip>
    );

  return null;
};

export default BalanceHeader;
