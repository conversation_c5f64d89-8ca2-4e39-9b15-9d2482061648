// libs
import { useMemo } from 'react';
import { Outlet } from 'react-router-dom';
import { Button, Divider, Layout } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';

// assets
import logoLight from '@/assets/medium-logo-light.png';
import logoDark from '@/assets/medium-logo-dark.png';

// components
import { DropdownLocale } from '@/components/DropdownAlpha';

// store
import { useThemeStore } from '@/store/useThemeStore';
import { Txt } from '@/components/TypographyMaster';

interface IPublicLayoutProps {}

const PublicLayout: React.FunctionComponent<IPublicLayoutProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isWhite, setIsWhite } = useThemeStore();

  // compute
  const isDevHint = useMemo(() => import.meta.env.DEV, []);

  const isDemoHint = useMemo(() => {
    const apiBase = import.meta.env.VITE_HUBS_ROOT;
    if (apiBase.toLowerCase().includes('demo')) return true;
    return false;
  }, []);

  return (
    <Layout className='min-h-screen items-center'>
      <div className='mt-20 p-2 lg:min-w-[1000px]'>
        <Layout.Header className='flex gap-x-4 bg-transparent p-0'>
          <img
            src={isWhite ? logoLight : logoDark}
            alt='logo'
          />

          <span
            style={{ fontWeight: '800' }}
            className='text-[28px] text-blue-600'
          >
            {isDevHint ? 'DEV' : ''} {isDemoHint ? 'DEMO' : ''}
          </span>
        </Layout.Header>
        <Layout.Content>
          <Outlet />
        </Layout.Content>
        <Layout.Footer className='bg-transparent p-0 text-right'>
          <Divider />
          <Button
            type='text'
            icon={isWhite ? <SunOutlined /> : <MoonOutlined />}
            onClick={() => setIsWhite()}
          />
          <DropdownLocale />
        </Layout.Footer>
      </div>
      <div style={{ position: 'fixed', left: 20, bottom: 20 }}>
        <Txt>Version: {import.meta.env.VITE_VERSION}</Txt>
      </div>
    </Layout>
  );
};

export default PublicLayout;
