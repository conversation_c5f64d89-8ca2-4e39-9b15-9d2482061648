// libs
import { Layout, Space } from 'antd';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  HomeOutlined,
  TeamOutlined,
  LogoutOutlined,
  LoginOutlined,
  HistoryOutlined,
  WalletOutlined,
  SecurityScanOutlined,
  SwapOutlined,
  BookOutlined,
} from '@ant-design/icons';

// hooks
import { useMerchantHub } from '@/hook';

// store
import { useThemeStore } from '@/store/useThemeStore';
import { useUserStore } from '@/store';

// layouts
import Menu from './components/Menu';
import PrivateHeader from './components/PrivateHeader';

interface IPrivateLayoutProps {}

const PrivateLayout: React.FunctionComponent<IPrivateLayoutProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { t } = useTranslation('privateLayout');
  const { isWhite } = useThemeStore();
  const { info } = useUserStore();

  useMerchantHub({});

  return (
    <Layout className='min-h-screen'>
      <Layout.Header className='sticky top-0 z-10 flex items-center justify-between shadow-sm '>
        <PrivateHeader />
      </Layout.Header>
      <Layout>
        <Layout.Sider theme={isWhite ? 'light' : 'dark'}>
          <Menu
            items={[
              {
                label: (
                  <Space>
                    <HomeOutlined />
                    {t('overview')}
                  </Space>
                ),
                to: '/',
                key: '/',
              },
              {
                label: (
                  <Space>
                    <LoginOutlined />
                    {t('deposit')}
                  </Space>
                ),
                to: 'deposit',
                key: 'deposit',
              },
              {
                label: (
                  <Space>
                    <LogoutOutlined />
                    {t('withdraw')}
                  </Space>
                ),
                to: 'withdraw',
                key: 'withdraw',
              },
              {
                label: (
                  <Space>
                    <SwapOutlined />
                    {t('diOrder')}
                  </Space>
                ),
                to: 'di-order',
                key: 'di-order',
              },
              {
                label: (
                  <Space>
                    <BookOutlined />
                    {t('ledger')}
                  </Space>
                ),
                to: 'ledger',
                key: 'ledger',
              },
              {
                label: (
                  <Space>
                    <HistoryOutlined />
                    {t('record')}
                  </Space>
                ),
                to: 'record',
                key: 'record',
              },
              {
                label: (
                  <Space>
                    <WalletOutlined />
                    {t('merchantWallet')}
                  </Space>
                ),
                to: 'wallet',
                key: 'wallet',
              },
              ...(info?.roles[0] === 'MerchantAdmin'
                ? [
                    {
                      label: (
                        <Space>
                          <TeamOutlined />
                          {t('subAccount')}
                        </Space>
                      ),
                      to: 'subAccount',
                      key: 'subAccount',
                    },
                  ]
                : []),
              ...(info?.roles[0] === 'MerchantAdmin'
                ? [
                    {
                      label: (
                        <Space>
                          <SecurityScanOutlined />
                          {t('ipWhitelist')}
                        </Space>
                      ),
                      to: 'ip',
                      key: 'ip',
                    },
                  ]
                : []),
            ]}
            prefix='/private'
          />
        </Layout.Sider>
        <Layout.Content className='p-3'>
          <Outlet />
        </Layout.Content>
      </Layout>
    </Layout>
  );
};

export default PrivateLayout;
