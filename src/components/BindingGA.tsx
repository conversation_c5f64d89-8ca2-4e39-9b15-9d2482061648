// libs
import { useCallback, useState } from 'react';
import { Button, Divider, Form, Input, QRCode, Skeleton, Space } from 'antd';
import { useQueryClient } from '@tanstack/react-query';
import { AndroidOutlined, ExclamationCircleOutlined, AppleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// apis
import { useEnable2FA } from '@/api';
import { use2FQrcode } from '@/api/auth/use2FQrcode';

// utils
import queryKeys from '@/utils/queryKeys';

// store
import { useNotifyStore, useUserStore } from '@/store';
import { useThemeStore } from '@/store/useThemeStore';

// components
import TableAlpha from '@/components/TableAlpha';
import ModalAlpha from '@/components/ModalAlpha';
import { Title, Txt } from '@/components/TypographyMaster';
import BtnFuncs from './BtnFuncs';

type SubmitEnable2FAValues = {
  FirstCode: string;
  SecondCode: string;
};

interface IBindingGAProps {
  open: boolean;
  onCancel: () => void;
}
const BindingGA: React.FunctionComponent<IBindingGAProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // states
  const [hoverOn, setHoverOn] = useState<'android' | 'ios'>();
  const [isHoverGoogleQrCode, setIsHoverGoogleQrCode] = useState(false);

  // hooks
  const queryClient = useQueryClient();
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('bindingGA');
  const { isDark, loginRes } = useUserStore();
  const { pushBSQ } = useNotifyStore();

  // mutation
  const {
    handleSearch,
    isPending: gettingQrCode,
    data,
  } = use2FQrcode({ isActive: open, onSettled: () => setIsHoverGoogleQrCode(false) });
  const { mutate: enable2FA, isPending: inEnable } = useEnable2FA({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successNotificationDescription') }]);
      onCancel();
      queryClient.refetchQueries({ queryKey: queryKeys.query.info(loginRes?.token) });
    },
  });
  // handlers
  const handleSubmitEnable = useCallback(
    (values: SubmitEnable2FAValues) => {
      enable2FA(values);
    },
    [enable2FA],
  );

  return (
    <ModalAlpha
      width={720}
      title={
        <header>
          <Title
            tight
            level={3}
          >
            {t('title')}
          </Title>
        </header>
      }
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        variant='filled'
        onFinish={handleSubmitEnable}
      >
        <Title
          color='green'
          level={4}
        >
          {t('firstStepTitle')}
        </Title>
        <TableAlpha
          dataSource={[null]}
          pagination={false}
          rowKey='key'
          columns={[
            {
              title: (
                <Space>
                  <Txt>{t('android')}</Txt>
                  <AndroidOutlined />
                </Space>
              ),
              key: 'android',
              align: 'center',
              render: () => {
                const isHovered = hoverOn === 'android';
                return (
                  <main className='flex justify-center'>
                    <div
                      onMouseEnter={() => setHoverOn('android')}
                      onMouseLeave={() => setHoverOn(undefined)}
                      className='relative'
                    >
                      <QRCode
                        bordered={false}
                        value='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=zh_TW'
                      />
                      <div
                        className={`absolute left-0
                        top-0 flex h-full w-full items-center justify-center transition-[opacity] duration-300
                        ${isDark ? 'bg-neutral-800' : 'bg-neutral-200'}
                        ${isHovered ? 'opacity-90' : 'opacity-0'}
                      `}
                      >
                        <a
                          href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=zh_TW'
                          target='_blank'
                          rel='noreferrer'
                        >
                          {t('gotoGoogleStore')}
                        </a>
                      </div>
                    </div>
                  </main>
                );
              },
            },
            {
              title: (
                <Space>
                  <Txt>{t('ios')}</Txt>
                  <AppleOutlined />
                </Space>
              ),
              key: 'ios',
              align: 'center',
              render: () => {
                const isHovered = hoverOn === 'ios';
                return (
                  <main className='flex justify-center'>
                    <div
                      onMouseEnter={() => setHoverOn('ios')}
                      onMouseLeave={() => setHoverOn(undefined)}
                      className='relative'
                    >
                      <QRCode
                        bordered={false}
                        value='https://apps.apple.com/tw/app/google-authenticator/id388497605'
                      />
                      <div
                        className={`absolute left-0
                        top-0 flex h-full w-full items-center justify-center transition-[opacity] duration-300
                        ${isDark ? 'bg-neutral-800' : 'bg-neutral-200'}
                        ${isHovered ? 'opacity-90' : 'opacity-0'}
                      `}
                      >
                        <a
                          href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=zh_TW'
                          target='_blank'
                          rel='noreferrer'
                        >
                          {t('gotoAppStore')}
                        </a>
                      </div>
                    </div>
                  </main>
                );
              },
            },
          ]}
          scroll={{ x: 400 }}
        />

        <Title
          color='green'
          level={4}
          className='mt-8'
          style={{ textWrap: 'wrap' }}
        >
          {t('secondStepTitle')}
        </Title>

        <Form.Item
          label={t('remarkLabel')}
          name='remark'
        >
          <Input
            className='w-36'
            placeholder={t('remarkPlaceholder')}
          />
        </Form.Item>

        <main className='flex justify-between'>
          {!gettingQrCode ? (
            <div
              onMouseEnter={() => setIsHoverGoogleQrCode(true)}
              onMouseLeave={() => setIsHoverGoogleQrCode(false)}
              className='relative'
            >
              <QRCode
                bordered
                value={data?.qrCodeUrl || '123'}
              />
              <div
                className={`absolute left-0
                        top-0 flex h-full w-full items-center justify-center transition-[opacity] duration-300
                        ${isDark ? 'bg-neutral-800' : 'bg-neutral-200'}
                        ${isHoverGoogleQrCode ? 'opacity-90' : 'opacity-0'}
                      `}
              >
                <BtnFuncs
                  iconType='redo'
                  type='link'
                  onClick={handleSearch}
                />
              </div>
            </div>
          ) : (
            <Skeleton.Node
              active
              style={{ width: 140, height: 140 }}
            />
          )}

          <Space
            direction='vertical'
            className='text-right'
          >
            <Txt strong>{t('scanInstruction')}</Txt>
            <Txt copyable>
              {t('myKey')}:
              {!gettingQrCode ? (
                data?.key
              ) : (
                <Skeleton.Input
                  size='small'
                  active
                />
              )}
            </Txt>
          </Space>
        </main>
        <Divider />

        <Title
          color='green'
          level={4}
          className='mt-8'
          style={{ textWrap: 'wrap' }}
        >
          {t('thirdStepTitle')}
        </Title>

        <Space>
          <Form.Item
            name='FirstCode'
            label={t('firstCodeLabel')}
            rules={[{ required: true, message: t('firstCodeErrorMessage') }]}
          >
            <Input
              disabled={inEnable}
              placeholder={t('firstCodePlaceholder')}
              autoComplete='off'
            />
          </Form.Item>

          <Form.Item
            name='SecondCode'
            label={t('secondCodeLabel')}
            rules={[{ required: true, message: t('secondCodeErrorMessage') }]}
          >
            <Input
              disabled={inEnable}
              placeholder={t('secondCodePlaceholder')}
              autoComplete='off'
            />
          </Form.Item>
        </Space>

        <section
          className={`
            mb-4 flex justify-end
          `}
        >
          <Txt
            className={`rounded border-2 p-2
              ${isWhite ? 'border-green-800' : 'border-[#52c41a]'}
            `}
            type='secondary'
            wrap
          >
            <ExclamationCircleOutlined /> {t('verifyMessage')}
          </Txt>
        </section>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inEnable}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </ModalAlpha>
  );
};

export default BindingGA;
