// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// utils
import { TxTransactionTypeMCOrder } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagTransactionTypeProps extends ITagAlphaProps {
  transactionType: TxTransactionTypeMCOrder;
}

const TagTransactionType: React.FunctionComponent<ITagTransactionTypeProps> = (props) => {
  // props
  const { transactionType, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagTransactionType');

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const typeMapping = {
    [TxTransactionTypeMCOrder.TransferIn]: { label: t('transferInLabel'), color: 'bg-blue-500' },
    [TxTransactionTypeMCOrder.TransferOut]: { label: t('transferOutLabel'), color: 'bg-orange-500' },
    default: { label: t('undefined'), color: 'bg-grey-500' },
  };

  const { label, color } = useMemo(() => {
    return typeMapping[transactionType] || typeMapping.default;
  }, [transactionType, typeMapping]);

  return (
    <TagAlpha
      className={`${color} ${className}`}
      {...tagProps}
    >
      <Txt>{label}</Txt>
    </TagAlpha>
  );
};

export default TagTransactionType;
export type { ITagTransactionTypeProps };
