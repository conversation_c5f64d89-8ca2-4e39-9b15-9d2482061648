// libs
import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  CloudServerOutlined,
  CloudUploadOutlined,
  ExclamationCircleOutlined,
  FileExcelOutlined,
  LoadingOutlined,
  ReloadOutlined,
  StopOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// utils
import { TxStatusMerchantOrder } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagStatusMcOrderProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: TxStatusMerchantOrder | undefined | null;
  onClick?: (status: ITagStatusMcOrderProps['status']) => void;
  tooltip?: string;
}

const TagStatusMcOrder: React.FunctionComponent<ITagStatusMcOrderProps> = (props) => {
  // props
  const { status, onClick, tooltip, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagStatusMcOrder');

  // compute
  // Icons
  const icon = useMemo(() => {
    switch (status) {
      case TxStatusMerchantOrder.Retry:
        return <ReloadOutlined />;
      case TxStatusMerchantOrder.Created:
        return <LoadingOutlined />;
      case TxStatusMerchantOrder.BlockchainBroadcast:
        return <CloudUploadOutlined />;
      case TxStatusMerchantOrder.BlockchainConfirmed:
        return <CloudServerOutlined />;
      case TxStatusMerchantOrder.Completed:
        return <CheckCircleOutlined />;
      case TxStatusMerchantOrder.Canceled:
        return <FileExcelOutlined />;
      case TxStatusMerchantOrder.Timeout:
        return <ClockCircleOutlined />;
      case TxStatusMerchantOrder.CallbackFailed:
        return <ExclamationCircleOutlined />;
      case TxStatusMerchantOrder.BlockchainFailed:
        return <CloseCircleOutlined />;
      case TxStatusMerchantOrder.OtherError:
        return <WarningOutlined />;

      default:
        return <StopOutlined />;
    }
  }, [status]);
  const color = useMemo(() => {
    switch (status) {
      case TxStatusMerchantOrder.Retry:
        return 'volcano';
      case TxStatusMerchantOrder.Created:
        return 'orange';
      case TxStatusMerchantOrder.BlockchainBroadcast:
        return 'gold';
      case TxStatusMerchantOrder.BlockchainConfirmed:
        return 'cyan';
      case TxStatusMerchantOrder.Completed:
        return 'success';
      case TxStatusMerchantOrder.Canceled:
        return 'red';
      case TxStatusMerchantOrder.Timeout:
        return 'error';
      case TxStatusMerchantOrder.CallbackFailed:
        return 'purple';
      case TxStatusMerchantOrder.BlockchainFailed:
        return 'geekblue';
      case TxStatusMerchantOrder.OtherError:
        return 'magenta';

      default:
        return 'default';
    }
  }, [status]);
  const label = useMemo(() => {
    switch (status) {
      case TxStatusMerchantOrder.Retry:
        return t('retry');
      case TxStatusMerchantOrder.Created:
        return t('created');
      case TxStatusMerchantOrder.BlockchainBroadcast:
        return t('blockchainBroadcast');
      case TxStatusMerchantOrder.BlockchainConfirmed:
        return t('blockchainConfirmed');
      case TxStatusMerchantOrder.Completed:
        return t('completed');
      case TxStatusMerchantOrder.Canceled:
        return t('canceled');
      case TxStatusMerchantOrder.Timeout:
        return t('timeout');
      case TxStatusMerchantOrder.CallbackFailed:
        return t('callbackFailed');
      case TxStatusMerchantOrder.BlockchainFailed:
        return t('blockchainFailed');
      case TxStatusMerchantOrder.OtherError:
        return t('otherError');

      default:
        return t('undefined');
    }
  }, [status, t]);

  // Click handler
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className='cursor-pointer'
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagStatusMcOrder;
export type { ITagStatusMcOrderProps };
