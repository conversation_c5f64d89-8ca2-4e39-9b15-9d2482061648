// libs
import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  FileExcelOutlined,
  HourglassOutlined,
  LoadingOutlined,
  UserOutlined,
  BankOutlined,
} from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// hooks
import { TradeStatusEnum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagTradeStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: TradeStatusEnum;
  onClick?: (status: ITagTradeStatusProps['status']) => void;
  tooltip?: string;
}

const TagTradeStatus: React.FunctionComponent<ITagTradeStatusProps> = (props) => {
  // props
  const { status, onClick, tooltip, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagTradeStatus');

  // compute
  const icon = useMemo(() => {
    if (status === TradeStatusEnum.Pending) return <ClockCircleOutlined />;
    if (status === TradeStatusEnum.Accepted) return <UserOutlined />;
    if (status === TradeStatusEnum.BankAccountAssigned) return <BankOutlined />;
    if (status === TradeStatusEnum.PaymentMade) return <DollarOutlined />;
    if (status === TradeStatusEnum.PaymentConfirmed) return <CheckCircleOutlined />;
    if (status === TradeStatusEnum.Completed) return <CheckCircleOutlined />;
    if (status === TradeStatusEnum.Cancelled) return <FileExcelOutlined />;
    if (status === TradeStatusEnum.Expired) return <HourglassOutlined />;
    return <LoadingOutlined />;
  }, [status]);

  const color = useMemo(() => {
    if (status === TradeStatusEnum.Pending) return 'processing';
    if (status === TradeStatusEnum.Accepted) return 'cyan';
    if (status === TradeStatusEnum.BankAccountAssigned) return 'blue';
    if (status === TradeStatusEnum.PaymentMade) return 'warning';
    if (status === TradeStatusEnum.PaymentConfirmed) return 'lime';
    if (status === TradeStatusEnum.Completed) return 'success';
    if (status === TradeStatusEnum.Cancelled) return 'volcano';
    if (status === TradeStatusEnum.Expired) return 'error';
    return 'default';
  }, [status]);

  const label = useMemo(() => {
    if (status === TradeStatusEnum.Pending) return t('pending');
    if (status === TradeStatusEnum.Accepted) return t('accepted');
    if (status === TradeStatusEnum.BankAccountAssigned) return t('bankAccountAssigned');
    if (status === TradeStatusEnum.PaymentMade) return t('paymentMade');
    if (status === TradeStatusEnum.PaymentConfirmed) return t('paymentConfirmed');
    if (status === TradeStatusEnum.Completed) return t('completed');
    if (status === TradeStatusEnum.Cancelled) return t('cancelled');
    if (status === TradeStatusEnum.Expired) return t('expired');
    return t('undefined');
  }, [status, t]);

  // handlers
  const handleClick = useCallback(() => {
    if (onClick) onClick(status);
  }, [onClick, status]);

  return (
    <TagAlpha
      className={`cursor-pointer ${className}`}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagTradeStatus;
export type { ITagTradeStatusProps };
