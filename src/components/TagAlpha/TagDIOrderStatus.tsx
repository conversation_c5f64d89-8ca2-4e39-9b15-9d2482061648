import { useMemo } from 'react';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  InfoOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ProxyOrderStatusEnum, proxyOrderStatusOptions } from '@/utils';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagDIOrderStatusProps extends Omit<ITagAlphaProps, 'onClick'> {
  status: ProxyOrderStatusEnum;
}

const TagDIOrderStatus: React.FunctionComponent<ITagDIOrderStatusProps> = (props) => {
  // props
  const { status, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const icon = useMemo(() => {
    if (status === ProxyOrderStatusEnum.Created) return <ClockCircleOutlined />;
    if (status === ProxyOrderStatusEnum.Completed) return <CheckCircleOutlined />;
    if (status === ProxyOrderStatusEnum.Cancelled) return <CloseCircleOutlined />;
    if (status === ProxyOrderStatusEnum.Expired) return <ExclamationCircleOutlined />;

    return <InfoOutlined />;
  }, [status]);

  const color = useMemo(() => {
    if (status === ProxyOrderStatusEnum.Created) return 'processing';
    if (status === ProxyOrderStatusEnum.Completed) return 'success';
    if (status === ProxyOrderStatusEnum.Cancelled) return 'error';
    if (status === ProxyOrderStatusEnum.Expired) return 'warning';

    return 'default';
  }, [status]);

  const label = useMemo(
    () => t(proxyOrderStatusOptions.find((option) => option.value === status)?.label || ''),
    [status, t],
  );

  return (
    <TagAlpha
      className={className}
      icon={icon}
      color={color}
      {...tagProps}
    >
      {label}
    </TagAlpha>
  );
};

export default TagDIOrderStatus;
export type { ITagDIOrderStatusProps };
