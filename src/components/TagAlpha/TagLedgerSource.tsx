import { useCallback, useMemo } from 'react';
import { ShopOutlined, ShoppingCartOutlined, UserOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { LedgerSourceEnum, ledgerSourceEnumOptions } from '@/utils';
import { Tooltip } from 'antd';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagLedgerSourceProps extends Omit<ITagAlphaProps, 'onClick'> {
  source: LedgerSourceEnum;
  onClick?: (status: ITagLedgerSourceProps['source']) => void;
  tooltip?: string;
}

const TagLedgerSource: React.FunctionComponent<ITagLedgerSourceProps> = (props) => {
  const { source, onClick, tooltip, className, ...tagProps } = props || {};

  const { t } = useTranslation('options');

  const icon = useMemo(() => {
    if (source === LedgerSourceEnum.ProxyOrder) return <ShoppingCartOutlined />;
    if (source === LedgerSourceEnum.MerchantTransaction) return <ShopOutlined />;
    if (source === LedgerSourceEnum.MemberTransaction) return <UserOutlined />;

    return null;
  }, [source]);
  const color = useMemo(() => {
    if (source === LedgerSourceEnum.ProxyOrder) return 'success';
    if (source === LedgerSourceEnum.MerchantTransaction) return 'processing';
    if (source === LedgerSourceEnum.MemberTransaction) return 'volcano';

    return 'default';
  }, [source]);

  const label = useMemo(
    () => t(ledgerSourceEnumOptions.find((option) => option.value === source)?.label || ''),
    [t, source],
  );

  const handleClick = useCallback(() => {
    if (onClick) onClick(source);
  }, [onClick, source]);

  return (
    <TagAlpha
      className={className}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagLedgerSource;
export type { ITagLedgerSourceProps };
