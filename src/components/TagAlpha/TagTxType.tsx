// libs
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// utils
import { TxCategoryNum } from '@/utils';

// components
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagTxTypeProps extends ITagAlphaProps {
  transactionType: TxCategoryNum;
}
const TagTxType: React.FunctionComponent<ITagTxTypeProps> = (props) => {
  // props
  const { transactionType, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('tagTxType');

  // compute
  const label = useMemo(() => {
    if (transactionType === TxCategoryNum.Deposit) return t('deposit');
    if (transactionType === TxCategoryNum.Commission) return t('commission');
    if (transactionType === TxCategoryNum.Transfer) return t('transfer');
    if (transactionType === TxCategoryNum.Withdraw) return t('withdraw');

    return t('undefined');
  }, [transactionType, t]);

  const color = useMemo(() => {
    if (transactionType === TxCategoryNum.Deposit) return 'bg-green-500';
    if (transactionType === TxCategoryNum.Commission) return 'bg-gold-500';
    if (transactionType === TxCategoryNum.Transfer) return 'bg-purple-500';
    if (transactionType === TxCategoryNum.Withdraw) return 'bg-red-500';

    return 'bg-grey-500';
  }, [transactionType]);

  return (
    <TagAlpha
      className={`${color} ${className}`}
      {...tagProps}
    >
      <Txt>{label}</Txt>
    </TagAlpha>
  );
};

export default TagTxType;
export type { ITagTxTypeProps };
