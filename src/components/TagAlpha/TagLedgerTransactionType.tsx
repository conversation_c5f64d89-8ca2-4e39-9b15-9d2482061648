import { useCallback, useMemo } from 'react';
import { ExportOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { LedgerTransactionTypeEnum, ledgerTransactionTypeEnumOptions } from '@/utils';
import { Tooltip } from 'antd';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';

interface ITagLedgerTransactionTypeProps extends Omit<ITagAlphaProps, 'onClick'> {
  type: LedgerTransactionTypeEnum;
  onClick?: (status: ITagLedgerTransactionTypeProps['type']) => void;
  tooltip?: string;
}

const TagLedgerTransactionType: React.FunctionComponent<ITagLedgerTransactionTypeProps> = (props) => {
  const { type, onClick, tooltip, className, ...tagProps } = props || {};

  const { t } = useTranslation('options');

  const icon = useMemo(() => {
    if (type === LedgerTransactionTypeEnum.Buy) return <ShoppingCartOutlined />;
    if (type === LedgerTransactionTypeEnum.Sell) return <ExportOutlined />;

    return null;
  }, [type]);
  const color = useMemo(() => {
    if (type === LedgerTransactionTypeEnum.Buy) return 'success';
    if (type === LedgerTransactionTypeEnum.Sell) return 'error';

    return 'default';
  }, [type]);

  const label = useMemo(
    () => t(ledgerTransactionTypeEnumOptions.find((option) => option.value === type)?.label || ''),
    [t, type],
  );

  const handleClick = useCallback(() => {
    if (onClick) onClick(type);
  }, [onClick, type]);

  return (
    <TagAlpha
      className={className}
      onClick={handleClick}
      icon={icon}
      color={color}
      {...tagProps}
    >
      <Tooltip title={tooltip}>{label}</Tooltip>
    </TagAlpha>
  );
};

export default TagLedgerTransactionType;
export type { ITagLedgerTransactionTypeProps };
