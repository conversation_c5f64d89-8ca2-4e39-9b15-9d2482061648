import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { DIOrderTypeEnum, diOrderTypeEnumOptions } from '@/utils';
import TagAlpha, { ITagAlphaProps } from './TagAlpha';
import { Txt } from '../TypographyMaster';

interface ITagDiOrderType extends ITagAlphaProps {
  type: DIOrderTypeEnum;
}
const TagDiOrderType: React.FunctionComponent<ITagDiOrderType> = (props) => {
  // props
  const { type, className, ...tagProps } = props || {};

  // hooks
  const { t } = useTranslation('options');

  // compute
  const label = useMemo(
    () => t(diOrderTypeEnumOptions.find((option) => option.value === type)?.label || 'undefined'),
    [type, t],
  );

  const color = useMemo(() => {
    if (type === DIOrderTypeEnum.Buy) return 'bg-green-500';
    if (type === DIOrderTypeEnum.Sell) return 'bg-red-500';

    return 'bg-grey-500';
  }, [type]);

  return (
    <TagAlpha
      className={`${color} ${className}`}
      {...tagProps}
    >
      <Txt>{label}</Txt>
    </TagAlpha>
  );
};

export default TagDiOrderType;
export type { ITagDiOrderType };
