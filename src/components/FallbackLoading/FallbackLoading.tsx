// store
import { useThemeStore } from '@/store/useThemeStore';

// components
import SpinMaster from '../SpinMaster';

interface IFallbackLoadingProps {}

const FallbackLoading: React.FunctionComponent<IFallbackLoadingProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { isWhite } = useThemeStore();

  return (
    <div
      className={`
        flex h-screen items-center justify-center
        ${isWhite ? 'bg-[#85A98F]' : 'bg-black'}
      `}
    >
      <SpinMaster />
    </div>
  );
};

export default FallbackLoading;
