import { useMemo, useRef } from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { GlobalOutlined } from '@ant-design/icons';
import { useThemeStore } from '@/store/useThemeStore';
import DropdownAlpha, { DropdownAlphaRef, IDropdownAlphaProps } from './DropdownAlpha';

type AllLocaleDefinition = Record<LocaleTypes, { locale: LocaleTypes; label: React.ReactNode }>;
interface IDropdownLocaleProps extends Omit<IDropdownAlphaProps, 'items'> {}

const DropdownLocale: React.FunctionComponent<IDropdownLocaleProps> = (props) => {
  // props
  const { ...dropdownProps } = props || {};

  // refs
  const dropRef = useRef<DropdownAlphaRef>(null);

  // hooks
  const { i18n } = useTranslation();
  const { language, changeLanguage } = i18n;
  const { isWhite } = useThemeStore();

  const allLocaleDefinition: AllLocaleDefinition = useMemo(() => {
    return {
      'zh-TW': {
        locale: 'zh-TW',
        label: '中文(繁體)',
      },
      'zh-CN': {
        locale: 'zh-CN',
        label: '中文(简体)',
      },
      'en-US': {
        locale: 'en-US',
        label: 'English',
      },
      'vi-VN': {
        locale: 'vi-VN',
        label: 'Tiếng Việt',
      },
    };
  }, []);

  const allLocaleItem = useMemo(() => {
    return Object.values(allLocaleDefinition)
      .filter((item) => item.locale !== 'zh-CN' && item.locale !== 'vi-VN') // Temporarily hide zh-CN and vi-VN until the translations are available
      .map((mapD) => {
        const { label, locale } = mapD;
        const isActive = language === locale;

        return {
          item: (
            <Button
              className={`${isWhite ? 'text-black' : 'text-white'} w-full`}
              type={isActive ? 'primary' : 'link'}
              onClick={() => {
                changeLanguage(locale);
                dropRef.current?.setIsOpen(false);
              }}
            >
              {label}
            </Button>
          ),
          key: locale,
          locale,
        };
      });
  }, [allLocaleDefinition, changeLanguage, isWhite, language]);

  return (
    <DropdownAlpha
      buttonProps={{ type: 'text', style: { paddingInline: 8 } }}
      ref={dropRef}
      icon={<GlobalOutlined />}
      items={allLocaleItem}
      noUnderLink
      gap={8}
      itemHeight='fit-content'
      pannelMaxHeight={400}
      {...dropdownProps}
    />
  );
};

export default DropdownLocale;
