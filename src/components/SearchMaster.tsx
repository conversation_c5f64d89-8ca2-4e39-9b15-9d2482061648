import { useCallback, useState } from 'react';
import { Flex, Input, Space } from 'antd';
import { FlexProps, InputProps } from 'antd/lib';
import { useTranslation } from 'react-i18next';
import { Txt } from './TypographyMaster';
import BtnFuncs from './BtnFuncs';

interface ISearchMasterProps extends Omit<InputProps, 'onSearch'> {
  titles: Array<{ label: React.ReactNode; key: string }>;
  onSearch?: (values: { [key in ISearchMasterProps['titles'][number]['key']]: string }) => void;
  containerProps?: FlexProps;
}

const SearchMaster: React.FunctionComponent<ISearchMasterProps> = (props) => {
  // props
  const { titles, onSearch, containerProps, ...searchProps } = props || {};

  // states
  const [values, setValues] = useState<{ [key in ISearchMasterProps['titles'][number]['key']]: string }>(
    titles.reduce((preValues, current) => ({ ...preValues, [current.key]: '' }), {}),
  );
  // hooks
  const { t } = useTranslation('searchMaster');

  // compute
  const handleSearch = useCallback(() => {
    if (onSearch) onSearch(values);
  }, [onSearch, values]);

  return (
    <Flex
      gap={8}
      className={`my-2 flex-wrap items-end ${containerProps?.className}`}
      {...containerProps}
    >
      {titles.map((mapT, index) => {
        const key = `${mapT.key}-${index}`;
        return (
          <Space
            key={key}
            size='small'
            className='flex flex-col items-start gap-y-1'
          >
            <Txt
              strong
              type='secondary'
            >
              {mapT.label}:
            </Txt>
            <Input
              placeholder={t('placeholder')}
              allowClear
              variant='filled'
              onKeyUp={(e) => {
                if (e.key === 'Enter') handleSearch();
              }}
              value={values[mapT.key]}
              onChange={(e) => {
                setValues((pre) => ({ ...pre, [mapT.key]: e.target.value }));
              }}
              {...searchProps}
            />
          </Space>
        );
      })}
      <BtnFuncs
        iconType='search'
        type='primary'
        shape='circle'
        onClick={() => {
          if (onSearch) onSearch(values);
        }}
      />
    </Flex>
  );
};

export default SearchMaster;
