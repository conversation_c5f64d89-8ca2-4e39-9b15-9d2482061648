// libs
import { Space } from 'antd';

// components
import BtnFuncs, { BtnCopy } from '../BtnFuncs';
import Txt from './Txt';

interface ITxtCompressibleProps {
  isWhite: boolean;
  isHovered: boolean;
  isExpanded: boolean;
  text: string | number | null | undefined;
  setIsHover: React.Dispatch<React.SetStateAction<Array<string>>>;
  setIsExpanded: React.Dispatch<React.SetStateAction<Array<string>>>;
}

const TxtCompressible: React.FunctionComponent<ITxtCompressibleProps> = (props) => {
  // props
  const { isWhite, isHovered, text, isExpanded, setIsHover, setIsExpanded } = props || {};

  const isValidText = text !== null && text !== undefined && text.toString().trim() !== '';

  return (
    <Space size='small'>
      <section
        className='relative'
        onMouseEnter={() => isValidText && setIsHover((pre) => [...pre, text.toString()])}
        onMouseLeave={() => isValidText && setIsHover((pre) => pre.filter((filterP) => filterP !== text))}
      >
        {isValidText ? (
          <Txt
            ellipsis={
              isExpanded
                ? undefined
                : {
                    suffix: text.toString().slice(-5),
                  }
            }
            className={`transition-[width] duration-300 ease-in-out ${isExpanded ? 'w-96' : 'w-24'}`}
          >
            {text}
          </Txt>
        ) : (
          <Txt>--</Txt>
        )}

        {/* mask */}
        {isValidText && (
          <BtnCopy
            text={text.toString()}
            className={`
              black absolute left-[50%] top-[50%]
              translate-x-[-50%] translate-y-[-50%] overflow-hidden border transition-all duration-300 ease-in-out
              ${isWhite ? 'border-green-500' : 'border-green-700'}
              ${isHovered ? 'border' : 'border-none'}
              ${isHovered ? 'w-[calc(100%+20px)]' : 'w-[80%]'}
              ${isHovered ? 'h-[calc(100%+20px)]' : 'h-[80%]'}
              ${isHovered ? 'opacity-100' : 'opacity-0'}
              ${isHovered ? 'rounded' : 'rounded-none'}
            `}
          />
        )}
      </section>

      {isValidText && (
        <BtnFuncs
          size='small'
          type='default'
          className='ml-1 transition-all duration-300 ease-in-out'
          iconType={isExpanded ? 'compression' : 'expand'}
          onClick={() =>
            setIsExpanded((pre) => (isExpanded ? pre.filter((filterP) => filterP !== text) : [...pre, text.toString()]))
          }
        />
      )}
    </Space>
  );
};

export default TxtCompressible;
