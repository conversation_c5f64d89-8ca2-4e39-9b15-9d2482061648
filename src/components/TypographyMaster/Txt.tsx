// libs
import React from 'react';
import { Typography } from 'antd';
import { TextProps } from 'antd/es/typography/Text';

interface ITxtProps extends TextProps {
  wrap?: boolean;
}

const Txt = React.forwardRef<HTMLElement, ITxtProps>((props, ref) => {
  // props
  const { className, wrap, ...textProps } = props || {};

  return (
    <Typography.Text
      ref={ref}
      {...{
        ...textProps,
        className: `
          ${wrap ? '' : 'text-nowrap'} ${className}
        `,
      }}
    />
  );
});

Txt.defaultProps = {
  wrap: false,
};

export default Txt;
