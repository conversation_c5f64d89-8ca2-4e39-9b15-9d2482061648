// libs
import { useCallback } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface IMainAccountVertifyProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (values: { operationPassword: string }) => void;
  loading?: boolean;
  form?: any;
}

const MainAccountVertify: React.FunctionComponent<IMainAccountVertifyProps> = (props) => {
  // props
  const { open, onCancel, onSubmit, loading, form } = props || {};

  // hooks
  const { t } = useTranslation('mainAccountVerify');

  const handleSubmit = useCallback(
    (values: { operationPassword: string }) => {
      onSubmit(values);
    },
    [onSubmit],
  );

  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={open}
        onCancel={onCancel}
        centered
      >
        <Form
          form={form}
          name={t('verify')}
          variant='filled'
          layout='vertical'
          initialValues={{ remember: true }}
          onFinish={handleSubmit}
        >
          <Form.Item
            label={t('operationPasswordLabel')}
            name='operationPassword'
          >
            <Input.Password
              prefix={<LockOutlined />}
              type={t('depositPassword')}
              placeholder={t('operationPasswordPlaceholder')}
            />
          </Form.Item>
          <Form.Item>
            <Button
              block
              type='primary'
              htmlType='submit'
              loading={loading}
            >
              {t('submit')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MainAccountVertify;
