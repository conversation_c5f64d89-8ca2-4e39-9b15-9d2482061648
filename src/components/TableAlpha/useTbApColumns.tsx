// libs
import { useState, useRef, useEffect, useMemo } from 'react';
import { Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { FilterDropdownProps, SortOrder } from 'antd/es/table/interface';
import { FilterOutlined, FilterFilled, CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import { isEqual } from 'lodash';

// components
import styles from './styles.module.scss';
import TbApFilterDropdown from './TbApFilterDropdown';

type TbApColumnsProps = {
  originColumns: ColumnsType<any> | undefined;
  onFilterChange?: (filters: { [key: string]: Array<string> }) => void;
  isDark?: boolean;
};
const useTbApColumns = (useProps: TbApColumnsProps) => {
  // props
  const { originColumns, onFilterChange, isDark } = useProps;

  /**
   * The variables used for selection and filtering are in the stage that has not yet been sent,
   * and the undetermined parts are cleared when then filter is closed
   */
  const [filteredValue, setFilteredValue] = useState<{ [key: string]: Array<string> }>({});
  /**
   * It has been send and compared through useEffect, If there is a value update,
   * onFilterChange will be triggered.
   */
  const [confirmFilterValue, setConfirmFilterValue] = useState<{ [key: string]: Array<string> }>({});

  // #: Event => onFilterChange
  const tempConfirmed = useRef(confirmFilterValue);
  useEffect(() => {
    const tempConfirmedCurrent = tempConfirmed.current;
    if (!onFilterChange || isEqual(tempConfirmedCurrent, confirmFilterValue)) return;
    onFilterChange(confirmFilterValue);
    tempConfirmed.current = confirmFilterValue;
  }, [confirmFilterValue, onFilterChange]);

  // Manually wrap the Columns again
  const columns = useMemo(() => {
    if (!Array.isArray(originColumns)) return undefined;
    return originColumns.map((originColumn) => {
      const { key: columnKey, filters, sorter } = originColumn;

      const filterIcon = (() => {
        if (!filters) return undefined;

        if (
          typeof columnKey === 'bigint' ||
          typeof columnKey === 'undefined' ||
          !(columnKey in confirmFilterValue) ||
          !confirmFilterValue[columnKey].length
        )
          // Filter default
          return (
            <FilterOutlined
              className={`
							${isDark ? styles.dark : ''}
							${styles.filter}
						`}
            />
          );
        // Filter Active
        const confirmedKeys = confirmFilterValue[columnKey];
        const confirmedLabels = confirmedKeys?.map((confirmKey) => {
          const filterInfo = filters?.find((filterOption) => filterOption.value.toString() === confirmKey.toString());
          return filterInfo?.text;
        });

        const tooltipContent =
          confirmedLabels && confirmedLabels.length > 0
            ? confirmedLabels.map((label, idx) => {
                const key = `${label}-${idx}`;
                return <div key={key}>{typeof label === 'string' ? label : <div className='p-1'>{label}</div>}</div>;
              })
            : 'No applied filter';

        return (
          <Tooltip title={<div>{tooltipContent}</div>}>
            <FilterFilled
              className={`
									${styles.filter}
									${isDark ? styles.dark : ''}
									${styles.active}
								`}
            />
          </Tooltip>
        );
      })();

      const sortIcon = ({ sortOrder }: { sortOrder: SortOrder }) => {
        if (!sorter) return undefined;
        return (
          <span className={styles['box-sorter']}>
            <CaretUpOutlined
              className={`${styles.sorter}
								${sortOrder === 'ascend' && styles.active}
							`}
            />
            <CaretDownOutlined
              className={`${styles.sorter}
								${sortOrder === 'descend' && styles.active}
							`}
            />
          </span>
        );
      };

      const filterDropdown = (() => {
        return filters
          ? (dropdownProps: FilterDropdownProps) => {
              return (
                <TbApFilterDropdown
                  {...{
                    columnKey,
                    filteredValue,
                    setFilteredValue,
                    setConfirmFilterValue,
                    ...dropdownProps,
                  }}
                />
              );
            }
          : undefined;
      })();

      const column: typeof originColumn = {
        filterIcon,
        sortIcon,
        filterDropdown,

        ...originColumn,
      };
      return column;
    });
  }, [confirmFilterValue, filteredValue, isDark, originColumns]);
  return columns;
};

export default useTbApColumns;
