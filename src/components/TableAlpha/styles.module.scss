@import '../../color.module.scss';

.test {
  border: 2px solid green;
}
.filter {
  padding-inline: 2px;
  transition: color 0.2s;
  color: $accessories;
  &:hover {
    color: rgb(192, 192, 192);
  }
  &:active {
    color: $accessories;
  }
}

.filter_clean {
  font-size: 14px;
  padding-inline: 2px;
  transition: color 0.2s;
  color: rgb(248, 222, 104);
  &:hover {
    color: rgb(240, 85, 58);
  }
  &:active {
    color: $accessories;
  }
}

.box-sorter {
  padding-inline: 2px;
  position: relative;
  display: flex;
  flex-direction: column;
  .sorter {
    font-size: 9px;
    transition: color 0.2s;
    color: $accessories;
    &.active {
      color: rgb(87, 255, 157);
    }
  }
}
