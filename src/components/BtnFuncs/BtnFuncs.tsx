// libs
import { RefObject, useMemo } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { IconBaseProps } from '@ant-design/icons/lib/components/Icon';
import {
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  ExpandOutlined,
  ExportOutlined,
  FullscreenExitOutlined,
  PlusCircleOutlined,
  RedoOutlined,
  SearchOutlined,
  SendOutlined,
  UnorderedListOutlined,
  PrinterOutlined,
} from '@ant-design/icons';

interface IBtnFuncsProps extends ButtonProps {
  iconType?:
    | 'export'
    | 'search'
    | 'compression'
    | 'expand'
    | 'edit'
    | 'remove'
    | 'add'
    | 'submit'
    | 'viewList'
    | 'redo'
    | 'disabled'
    | 'print';
  iconProps?: IconBaseProps;
  tooltip?: string;
  ref?: RefObject<HTMLButtonElement>;
}

const BtnFuncs: React.FunctionComponent<IBtnFuncsProps> = (props) => {
  const { iconType, icon, iconProps, tooltip, ...btnProps } = props || {};
  const iconDefinition = useMemo(() => {
    if (icon) return icon;
    if (iconType === 'export') return <ExportOutlined {...iconProps} />;
    if (iconType === 'search') return <SearchOutlined {...iconProps} />;
    if (iconType === 'edit') return <EditOutlined {...iconProps} />;
    if (iconType === 'remove') return <DeleteOutlined {...iconProps} />;
    if (iconType === 'add') return <PlusCircleOutlined {...iconProps} />;
    if (iconType === 'submit') return <SendOutlined {...iconProps} />;
    if (iconType === 'viewList') return <UnorderedListOutlined {...iconProps} />;
    if (iconType === 'redo') return <RedoOutlined {...iconProps} />;
    if (iconType === 'disabled') return <CloseCircleOutlined {...iconProps} />;
    if (iconType === 'expand') return <ExpandOutlined {...iconProps} />;
    if (iconType === 'compression') return <FullscreenExitOutlined {...iconProps} />;
    if (iconType === 'print') return <PrinterOutlined {...iconProps} />;
    return undefined;
  }, [icon, iconType, iconProps]);

  return (
    <Tooltip title={tooltip}>
      <Button
        {...btnProps}
        icon={iconDefinition}
      />
    </Tooltip>
  );
};

export default BtnFuncs;
