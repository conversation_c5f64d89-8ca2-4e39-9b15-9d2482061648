/* eslint-disable no-nested-ternary */
// libs
import { memo, useCallback, useMemo, useState } from 'react';
import { ButtonProps, Tooltip } from 'antd';
import { CheckCircleFilled, CopyOutlined, CloseCircleOutlined } from '@ant-design/icons';

// utils
import { logInfo } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';

interface IBtnCopyProps extends Omit<ButtonProps, 'onClick' | 'type'> {
  text: string;
  onClick?: (text: string) => void;
  tooltips?: [string, string];
}

const BtnCopy: React.FunctionComponent<IBtnCopyProps> = (props) => {
  // props
  const { text, onClick, tooltips, className, ...btnProps } = props || {};

  // states
  const [isCopying, setIsCopying] = useState(false);
  const [isCopyFailed, setIsCopyFailed] = useState(false);

  // hooks
  const { isWhite } = useThemeStore();

  // Fallback logic for copying text
  const copyFallback = useCallback(
    (textToCopy: string) => {
      try {
        const tempInput = document.createElement('textarea');
        tempInput.value = textToCopy;
        tempInput.style.position = 'absolute';
        tempInput.style.left = '-9999px';
        document.body.appendChild(tempInput);
        tempInput.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(tempInput);
        logInfo(`Copy success ${text}`);

        return successful;
      } catch {
        return false;
      }
    },
    [text],
  );

  // Handle button click
  const handleOnClick = useCallback(() => {
    if (onClick) onClick(text);
    setIsCopying(true);

    if (navigator.clipboard?.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          setIsCopyFailed(false);
        })
        .catch(() => {
          const success = copyFallback(text);
          setIsCopyFailed(!success);
        })
        .finally(() => {
          setTimeout(() => setIsCopying(false), 1000);
        });
    } else {
      const success = copyFallback(text);
      setIsCopyFailed(!success);
      setTimeout(() => setIsCopying(false), 1000);
    }
  }, [copyFallback, onClick, text]);

  const tooltipTitle = useMemo(() => {
    if (isCopying) {
      return isCopyFailed ? 'Copy failed' : tooltips?.[1] || 'Copied';
    }
    return tooltips?.[0] || 'Copy';
  }, [isCopying, isCopyFailed, tooltips]);

  return (
    <Tooltip title={tooltipTitle}>
      <button
        type='button'
        onClick={handleOnClick}
        className={`
          ${isWhite ? 'text-green-400' : 'text-green-700'}
          ${className}
        `}
        {...btnProps}
      >
        {isCopying ? (
          isCopyFailed ? (
            <CloseCircleOutlined className={`${isWhite ? 'text-red-600' : 'text-red-400'}`} />
          ) : (
            <CheckCircleFilled className={`${isWhite ? 'text-green-600' : 'text-green-400'}`} />
          )
        ) : (
          <CopyOutlined />
        )}
      </button>
    </Tooltip>
  );
};

export default memo(BtnCopy);
