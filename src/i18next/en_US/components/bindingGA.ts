export default {
  successNotificationDescription: '2FA setup successful',
  title: 'Set up 2FA authentication via Google Authenticator',
  firstStepTitle: '1. Download Google Authenticator',
  android: 'Android',
  gotoGoogleStore: 'Go to Google Store',
  ios: 'IOS',
  gotoAppStore: 'Go to App Store',
  secondStepTitle:
    '2. After opening Google Authenticator on your phone, click the + sign in the lower right corner to add a new account.',
  remarkLabel: 'Name (for notes)',
  remarkPlaceholder: 'Please enter',
  scanInstruction: 'Scan this with the Google Authenticator app',
  myKey: 'My key',
  thirdStepTitle: '3. Enter the verification code generated by Google Authenticator',
  firstCodeLabel: 'First verification code',
  firstCodeErrorMessage: 'Please enter',
  firstCodePlaceholder: 'Please enter',
  secondCodeLabel: 'Second verification code',
  secondCodeErrorMessage: 'Please enter',
  secondCodePlaceholder: 'Please enter',
  verifyMessage:
    'To ensure the accuracy of verification when binding the verification code, please wait and enter the verification code twice in a row.',
  submit: 'Binding',
};
