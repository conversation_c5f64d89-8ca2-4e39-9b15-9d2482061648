export default {
  orderType: 'Order type',
  memberOrderCreation: 'Member order creation',
  merchantOrderNumber: 'Member order number',
  orderId: 'Order ID',
  createdAt: 'Created at',
  confirmedAt: 'Confirmed at',
  status: 'Status',
  applicant: 'Applicant',
  requireAmount: 'Require amount',
  currency: 'Currency',
  fees: 'Fees',
  fee: 'Fee',
  gas: 'Gas',
  protocol: 'Protocol',
  actualAmount: 'Actual amount',
  to: 'Allocate wallet',
  publicChainDetails: 'Public chain details',
  remark: 'Remark',
  log: 'log',
  title: 'Order details',
  verifiedButton: 'Verified',
  determinedButton: 'To be determined',
};
