export default {
  breadcrumbHome: 'Home',
  breadcrumbCurrent: 'DI Orders',
  statusLabel: 'Status',
  statusPlaceholder: 'Please select status',
  orderUidLabel: 'Order ID',
  merchantOrderIdLabel: 'Merchant Order ID',
  createButton: 'Create',
  fileName: 'DI Orders',
  exportButton: 'Export',
  orderUidTitleRow: 'Order ID',
  redirectUrlTitleRow: 'Redirect URL',
  merchantOrderIdTitleRow: 'Merchant Order ID',
  memberIdTitleRow: 'Member ID',
  payerBankAccountNameTitleRow: 'Payer Bank Account Name',
  entryCodeTitleRow: 'Entry Code',
  transactionTypeTitleRow: 'Transaction Type',
  cryptoTypeTitleRow: 'Crypto Type',
  cryptoAmountTitleRow: 'Crypto Amount',
  fiatTypeTitleRow: 'Fiat Type',
  fiatAmountTitleRow: 'Fiat Amount',
  createdAtTitleRow: 'Created At',
  merchantNotifiedTitleRow: 'Merchant Notification Status',
  statusTitleRow: 'Status',
};
