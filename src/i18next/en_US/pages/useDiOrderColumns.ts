export default {
  orderUid: 'Order ID',
  redirectUrl: 'Redirect URL',
  merchantOrderId: 'Merchant Order ID',
  memberId: 'Member ID',
  payerBankAccountName: 'Payer Bank Account Name',
  entryCode: 'Entry Code',
  transactionType: 'Transaction Type',
  cryptoInfo: 'Crypto Information',
  cryptoType: 'Crypto Type',
  cryptoAmount: 'Crypto Amount',
  fiatInfo: 'Fiat Information',
  fiatType: 'Fiat Type',
  totalFee: 'Total Fee',
  transferAmount: 'Transfer Amount',
  cryptoDetails: 'Crypto Details',
  fiatAmount: 'Fiat Amount',
  createdAt: 'Created At',
  merchantNotified: 'Merchant Notification Status',
  notified: 'Notified',
  notNotified: 'Not Notified',
  status: 'Status',
  statusTooltip: 'Click to view details',
};
