export default {
  trc20Label: 'TRC20 wallet',
  breadcrumbHome: 'Overview',
  breadcrumbCurrent: 'Merchant wallet',
  title: 'Merchant wallet',
  alertMessage:
    'Please make sure that the selected [Currency-Protocol] matches the protocol type of the receiving wallet or exchange. Otherwise you will not be able to recover your lost cryptocurrency.',
  currencyPlaceholder: 'Currency',
  fileName: 'Transaction records',
  hashLabel: 'Hash',
  typeTitleRow: 'Type',
  merchantNumberTitleRow: 'Merchant Number',
  merchantNameTitleRow: 'Merchant Name',
  createdAtTitleRow: 'Created At',
  confirmedAtTitleRow: 'Confirmed At',
  applicantTitleRow: 'Applicant',
  approverTitleRow: 'Approver',
  transactionHashTitleRow: 'Hash',
  fromAddressTitleRow: 'From Address',
  toAddressTitleRow: 'To Address',
  cryptoTitleRow: 'Crypto',
  consumedTitleRow: 'Consumed (TRX)',
  feeTitleRow: 'Fee',
  requiredTitleRow: 'Required',
  actualTitleRow: 'Actual',
  statusTitleRow: 'Status',
  remarkTitleRow: 'Remark',
};
