export default {
  orderType: 'Order type',
  deposit: 'Deposit',
  orderNumber: 'UXM order number',
  notAvailable: 'Not available',
  serialNumber: 'Serial number',
  from: 'From',
  to: 'To',
  createdAt: 'Created at',
  confirmedAt: 'Confirmed at',
  status: 'Status',
  applicant: 'Applicant',
  requireAmount: 'Require amount',
  currency: 'Currency',
  fees: 'Fees',
  fee: 'Fee',
  gas: 'Gas',
  protocol: 'Protocl',
  actualAmount: 'Actual amount',
  publicChainDetails: 'Public chain details',
  remark: 'Remark',
  log: 'log',
  title: 'Order details',
  verifyButton: 'Verified',
  determinedButton: 'To be determined',
};
