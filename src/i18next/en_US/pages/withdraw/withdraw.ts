export default {
  breadcrumbHome: 'Overview',
  breadcrumbCurrent: 'Withdraw',
  fileName: 'Transaction records',
  statusLabel: 'Status',
  statusPlaceholder: 'Status',
  orderUidLabel: 'Order UID',
  merchantOrderIdLabel: 'Merchant order number',
  hashLabel: 'Hash',
  merchantNumberTitleRow: 'Merchant Number',
  merchantNameTitleRow: 'Merchant Name',
  typeTitleRow: 'Type',
  createdAtTitleRow: 'Created At',
  confirmedAtTitleRow: 'Confirmed At',
  orderUidTitleRow: 'Order UID',
  merchantOrderNumberTitleRow: 'Merchant Order Number',
  transactionHashTitleRow: 'Hash',
  fromAddressTitleRow: 'From Address',
  toAddressTitleRow: 'To Address',
  cryptoTitleRow: 'Crypto',
  consumedTitleRow: 'Consumed (TRX)',
  feeTitleRow: 'Fee',
  requiredTitleRow: 'Required',
  actualTitleRow: 'Actual',
  transferTitleRow: 'Transfer',
  statusTitleRow: 'Status',
  remarkTitleRow: 'Remark',
};
