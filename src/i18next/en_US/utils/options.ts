export default {
  undefined: 'Undefined',

  txStatusCreated: 'Waiting for payment',
  txStatusBroadcasted: 'Blockchain broadcasting',
  txStatusConfirmed: 'Blockchain confirmation',
  txStatusCompleted: 'Finish',
  txStatusCanceled: 'Cancel',
  txStatusTimeout: 'Timeout',
  txStatusRetry: 'Retry',
  txStatusCallbackFailed: 'Callback failed',
  txStatusTransactionFailed: 'Blockchain failed',

  normalTronMasterStatus: 'Normal',
  depositOnlyTronMasterStatus: 'Deposit only',
  withdrawalOnlyTronMasterStatus: 'Withdrawal only',
  suspendedTronMasterStatus: 'Suspended',

  depositTxCategoryNum: 'Deposit',
  withdrawTxCategoryNum: 'Withdraw',
  transferTxCategoryNum: 'Transfer',
  commissionTxCategoryNum: 'Commission',

  buyDIOrderTypeEnum: 'Buy',
  sellDIOrderTypeEnum: 'Sell',

  pendingTradeStatusEnum: 'Pending',
  acceptedTradeStatusEnum: 'Accepted',
  bankAccountAssignedTradeStatusEnum: 'Bank Account Assigned',
  paymentMadeTradeStatusEnum: 'Payment Made',
  paymentConfirmedTradeStatusEnum: 'Payment Confirmed',
  completedTradeStatusEnum: 'Completed',
  cancelledTradeStatusEnum: 'Cancelled',
  expiredTradeStatusEnum: 'Expired',

  retryTxStatusMerchantOrder: 'Retry',
  createdTxStatusMerchantOrder: 'Created',
  blockchainBroadcastTxStatusMerchantOrder: 'Broadcast',
  blockchainConfirmedTxStatusMerchantOrder: 'Confirmed',
  completedTxStatusMerchantOrder: 'Completed',
  canceledTxStatusMerchantOrder: 'Cancelled',
  timeoutTxStatusMerchantOrder: 'Timeout',
  callbackFailedTxStatusMerchantOrder: 'Callback Failed',
  blockchainFailedTxStatusMerchantOrder: 'Blockchain Failed',
  otherTxStatusMerchantOrder: 'Other',

  createdProxyOrderStatusEnum: 'Created',
  completedProxyOrderStatusEnum: 'Completed',
  cancelledProxyOrderStatusEnum: 'Cancelled',
  expiredProxyOrderStatusEnum: 'Order matching failed',

  proxyOrderLedgerSourceEnum: 'Proxy Order',
  merchantTransactionLedgerSourceEnum: 'Merchant Transaction',
  memberTransactionLedgerSourceEnum: 'Member Transaction',

  buyLedgerTransactionTypeEnum: 'Buy',
  sellLedgerTransactionTypeEnum: 'Sell',
};
