export default {
  successNotificationDescription: '設定2FA 成功',
  title: '透過Google Authenticator 設定 2FA 驗證',
  firstStepTitle: '1. 下載 Google Authenticator',
  android: '安卓',
  gotoGoogleStore: '前往 Google Store',
  ios: 'IOS',
  gotoAppStore: '前往 App Store',
  secondStepTitle: '2. 使用手機開啟 Google Authenticator 後，點擊右下角 + 號新增帳戶',
  remarkLabel: '名稱 (備註用)',
  remarkPlaceholder: '請輸入',
  scanInstruction: '使用 Google Authenticator 應用程式掃描此內容',
  myKey: '我的金鑰',
  thirdStepTitle: '3. 輸入Google Authenticator 生成的驗證碼',
  firstCodeLabel: '第一驗證碼',
  firstCodeErrorMessage: '請輸入',
  firstCodePlaceholder: '請輸入',
  secondCodeLabel: '第二驗證碼',
  secondCodeErrorMessage: '請輸入',
  secondCodePlaceholder: '請輸入',
  verifyMessage: '綁定驗證碼時為求驗證精確，請等待並輸入接連兩次刷新的驗證碼',
  submit: '綁定',
};
