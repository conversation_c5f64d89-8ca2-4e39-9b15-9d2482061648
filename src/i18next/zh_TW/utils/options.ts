export default {
  undefined: '未定義',

  txStatusCreated: '等待付款',
  txStatusBroadcasted: '區塊鏈廣播',
  txStatusConfirmed: '區塊鏈確認',
  txStatusCompleted: '完成',
  txStatusCanceled: '取消',
  txStatusTimeout: '超時',
  txStatusRetry: '重試',
  txStatusCallbackFailed: '通知商戶失敗',

  normalTronMasterStatus: '普通的',
  depositOnlyTronMasterStatus: '限存款',
  withdrawalOnlyTronMasterStatus: '僅限提款',
  suspendedTronMasterStatus: '暫停',
  txStatusTransactionFailed: '區塊鏈失敗',

  depositTxCategoryNum: '入金',
  withdrawTxCategoryNum: '出金',
  transferTxCategoryNum: '轉帳',
  commissionTxCategoryNum: '佣金',

  buyDIOrderTypeEnum: '買入',
  sellDIOrderTypeEnum: '賣出',

  pendingTradeStatusEnum: '待處理',
  acceptedTradeStatusEnum: '已接受',
  bankAccountAssignedTradeStatusEnum: '已分配銀行帳戶',
  paymentMadeTradeStatusEnum: '已付款',
  paymentConfirmedTradeStatusEnum: '付款已確認',
  completedTradeStatusEnum: '已完成',
  cancelledTradeStatusEnum: '已取消',
  expiredTradeStatusEnum: '已過期',

  retryTxStatusMerchantOrder: '重試',
  createdTxStatusMerchantOrder: '已創建',
  blockchainBroadcastTxStatusMerchantOrder: '已廣播',
  blockchainConfirmedTxStatusMerchantOrder: '已確認',
  completedTxStatusMerchantOrder: '已完成',
  canceledTxStatusMerchantOrder: '已取消',
  timeoutTxStatusMerchantOrder: '逾時',
  callbackFailedTxStatusMerchantOrder: '回調失敗',
  blockchainFailedTxStatusMerchantOrder: '區塊鏈失敗',
  otherTxStatusMerchantOrder: '其他',

  createdProxyOrderStatusEnum: '已建立',
  completedProxyOrderStatusEnum: '已完成',
  cancelledProxyOrderStatusEnum: '已取消',
  expiredProxyOrderStatusEnum: '配對失敗',

  proxyOrderLedgerSourceEnum: '跳轉訂單',
  merchantTransactionLedgerSourceEnum: '商戶訂單',
  memberTransactionLedgerSourceEnum: '會員訂單',

  buyLedgerTransactionTypeEnum: '買入',
  sellLedgerTransactionTypeEnum: '賣出',
};
