const validatePassword = (_: any, value: string) => {
  if (!value) {
    return Promise.reject(new Error('請輸入密碼'));
  }
  if (value.length < 8) {
    return Promise.reject(new Error('密碼必須至少包含 8 個字符'));
  }
  if (!/[a-z]/.test(value)) {
    return Promise.reject(new Error('密碼必須至少包含一個小寫字母 (a-z)'));
  }
  if (!/[A-Z]/.test(value)) {
    return Promise.reject(new Error('密碼必須至少包含一個大寫字母 (A-Z)'));
  }
  return Promise.resolve();
};

export default validatePassword;
