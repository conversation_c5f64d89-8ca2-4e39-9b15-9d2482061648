// libs
import axios from 'axios';

// apis
import { type LoginRes } from '@/api/auth/useLogin';

// utils
import { forage } from './foragePkg';

const axiosRoot = axios.create({
  baseURL: `${import.meta.env.VITE_AXIOS_ROOT}`,
});

axiosRoot.interceptors.request.use(async (config) => {
  const auth = await forage<LoginRes>().getItem('loginRes');
  if (!auth) return config;
  const pureConfig = { ...config };
  pureConfig.headers.Authorization = `Bearer ${auth.token}`;
  return pureConfig;
});

// eslint-disable-next-line import/prefer-default-export
export { axiosRoot };
