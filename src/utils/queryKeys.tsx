// apis
import type {
  IpListProps,
  McOrderListProps,
  McUserListProps,
  MerchantListProps,
  OderListMerchantOptionsProps,
  DiOrderListProps,
} from '@/api';

export default {
  query: {
    info: (token: string | undefined) => ['info', token],
    cliensMerchants: (customerName: string | undefined) => ['client', 'merchants', customerName],
    // member order
    orderList: (OderListMerchantOptionsProps?: OderListMerchantOptionsProps) => {
      const result = [
        'order',
        'member',
        ...Object.values(OderListMerchantOptionsProps || {}).map((value) =>
          Array.isArray(value) ? JSON.stringify(value) : value,
        ),
      ];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.orderListKeys) {
          const isAlreadyHave = window.myGlobalVariable.orderListKeys.every((everyKeys) => {
            return everyKeys.every((everyKey, index) => everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.orderListKeys.push(result);
        } else window.myGlobalVariable.orderListKeys = [result];
      } else window.myGlobalVariable = { orderListKeys: [result] };
      return result;
    },
    allOrderList: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.orderListKeys || [];
      return [];
    },
    // merchant order
    merchantOrderList: (MerchantOrderListOptionsProps?: McOrderListProps) => {
      const result = [
        'order',
        'merchant',
        'self',
        ...Object.values(MerchantOrderListOptionsProps || {}).map((value) =>
          Array.isArray(value) ? JSON.stringify(value) : value,
        ),
      ];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.merchantOrderListKeys) {
          const isAlreadyHave = window.myGlobalVariable.merchantOrderListKeys.some((existingKeys) => {
            return JSON.stringify(existingKeys) === JSON.stringify(result);
          });
          if (!isAlreadyHave) window.myGlobalVariable.merchantOrderListKeys.push(result);
        } else window.myGlobalVariable.merchantOrderListKeys = [result];
      } else window.myGlobalVariable = { merchantOrderListKeys: [result] };
      return result;
    },
    allMerchantOrderList: () => {
      if (window.myGlobalVariable?.merchantOrderListKeys?.length) {
        return window.myGlobalVariable.merchantOrderListKeys;
      }
      return [];
    },
    // user list
    usertList: (params: McUserListProps | undefined) => {
      const result = ['merchant', 'userList', 'self', ...Object.values(params || {})];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.userListKeys) {
          const isAlreadyHave = window.myGlobalVariable.userListKeys.some((everyKeys) => {
            return everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index));
          });
          if (!isAlreadyHave) window.myGlobalVariable.userListKeys.push(result);
        } else window.myGlobalVariable.userListKeys = [result];
      } else window.myGlobalVariable = { userListKeys: [result] };
      return result;
    },
    allUserList: () => {
      if (window.myGlobalVariable) return window.myGlobalVariable.userListKeys || [];
      return [];
    },
    // merchant details(balance)
    merchantDetailBalance: (MerchantDetailBalanceProps?: MerchantListProps) => {
      const result = [
        'merchant',
        'detail',
        'balance',
        ...Object.values(MerchantDetailBalanceProps || {}).map((value) => {
          if (Array.isArray(value)) return JSON.stringify(value);
          if (
            typeof value === 'string' ||
            typeof value === 'number' ||
            typeof value === 'boolean' ||
            value === null ||
            value === undefined
          ) {
            return value;
          }
          return JSON.stringify(value);
        }),
      ];

      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.balanceKeys) {
          const isAlreadyHave = window.myGlobalVariable.balanceKeys.some((existingKeys) => {
            return JSON.stringify(existingKeys) === JSON.stringify(result);
          });
          if (!isAlreadyHave) window.myGlobalVariable.balanceKeys.push(result);
        } else window.myGlobalVariable.balanceKeys = [result];
      } else window.myGlobalVariable = { balanceKeys: [result] };
      return result;
    },
    allMerchantDetailBalance: () => {
      if (window.myGlobalVariable?.balanceKeys?.length) {
        return window.myGlobalVariable.balanceKeys;
      }
      return [];
    },
    // Ip whitelist
    ipWhiteList: (params: IpListProps | undefined) => {
      const result: (string | number)[] = [
        'ip',
        'list',
        'merchant',
        ...(Object.values(params || {}) as (string | number)[]),
      ];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.ipWhiteListKeys) {
          const isAlreadyHave = window.myGlobalVariable.ipWhiteListKeys.some((everyKeys) =>
            everyKeys.every((everyKey, index) => !everyKey || everyKey === result.at(index)),
          );
          if (!isAlreadyHave) window.myGlobalVariable.ipWhiteListKeys.push(result);
        } else window.myGlobalVariable.ipWhiteListKeys = [result];
      } else window.myGlobalVariable = { ipWhiteListKeys: [result] };
      return result;
    },
    allIpWhiteList: () => {
      if (window.myGlobalVariable?.ipWhiteListKeys?.length) {
        return window.myGlobalVariable.ipWhiteListKeys;
      }
      return [];
    },
    // DI order list
    diOrderList: (params: DiOrderListProps | undefined) => {
      const result = [
        'order',
        'di',
        'payment',
        ...Object.values(params || {}).map((value) => (Array.isArray(value) ? JSON.stringify(value) : value)),
      ];
      if (window.myGlobalVariable) {
        if (window.myGlobalVariable.diOrderListKeys) {
          const isAlreadyHave = window.myGlobalVariable.diOrderListKeys.some((existingKeys) => {
            return JSON.stringify(existingKeys) === JSON.stringify(result);
          });
          if (!isAlreadyHave) window.myGlobalVariable.diOrderListKeys.push(result as any);
        } else window.myGlobalVariable.diOrderListKeys = [result as any];
      } else window.myGlobalVariable = { diOrderListKeys: [result as any] };
      return result;
    },
    allDiOrderList: () => {
      if (window.myGlobalVariable?.diOrderListKeys?.length) {
        return window.myGlobalVariable.diOrderListKeys;
      }
      return [];
    },
  },
};
