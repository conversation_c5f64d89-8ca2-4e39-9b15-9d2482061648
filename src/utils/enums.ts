enum VerifyStatusNum {
  Verifing = 1,
  Pass = 101,
  Fail = 501,
}

enum CountryKeyNum {
  Taiwan = 'tw',
  Honkon = 'hk',
}

enum CryptoEnum {
  TRC20_USDT = 1,
  ERC20_USDT = 2,
}

enum TxCategoryNum {
  Deposit = 1,
  Withdraw = 2,
  Transfer = 3,
  Commission = 4,
}

enum TxTransactionTypeMCOrder {
  TransferIn = 1,
  TransferOut = 2,
}

enum TxStatusNum {
  Created = 1, // created
  Broadcasted = 2, // 區塊鏈廣播
  Confirmed = 3, // 區塊鏈確認
  Completed = 4, // 完成
  Canceled = 100, // 取消
  Timeout = 101, // 超時
  Retry = -1, // 重試
  MerchantCallbackFailed = 1001, // 通知商戶的回調失敗
  BlockchainTransactionFailed = 1002, // 區塊鏈交易失敗
}

enum TxStatusMerchantOrder {
  Retry = -1,
  Created = 1,
  BlockchainBroadcast = 2,
  BlockchainConfirmed = 3,
  Completed = 4,
  Canceled = 100,
  Timeout = 101,
  CallbackFailed = 1001,
  BlockchainFailed = 1002,
  OtherError = 1003,
}

enum TronMasterStatusEnum {
  Normal = 0,
  DepositOnly = 1,
  WithdrawalOnly = 2,
  Suspended = 3,
}

enum TradeStatusEnum {
  Pending = 0, // Order created, waiting for agent
  Accepted = 1, // Agent accepted, bank not set
  BankAccountAssigned = 2, // Bank set, waiting for user to pay
  PaymentMade = 3, // User paid
  PaymentConfirmed = 4, // Agent confirmed payment
  Completed = 5, // Trade finished
  Cancelled = 6, // Trade cancelled
  Expired = 7, // Trade expired
}

enum DIOrderTypeEnum {
  Buy = 1,
  Sell = 2,
}

enum ProxyOrderStatusEnum {
  Created = 0,
  Completed = 1,
  Cancelled = 2,
  Expired = 3,
}

enum LedgerSourceEnum {
  ProxyOrder = 1,
  MerchantTransaction = 2,
  MemberTransaction = 3,
}

enum LedgerTransactionTypeEnum {
  Buy = 1,
  Sell = 2,
}

const eNumEntities = <T extends object>(originEnum: T) => {
  const keyofEnums = Object.keys(originEnum);
  const vailedKeys = keyofEnums.filter((filterK) => Number.isNaN(Number(filterK)));

  const result = vailedKeys.reduce(
    (arr, key) => {
      const enumKey = key as keyof typeof originEnum;
      const valueOfObj = originEnum[enumKey];
      if (!arr.keys.includes(enumKey) && valueOfObj !== undefined)
        return {
          keys: [...arr.keys, enumKey],
          values: [...arr.values, valueOfObj],
        };
      if (keyofEnums.includes('NoTrade')) {
        // logInfo({ Title: 'Unvalid', enumKey, valueOfObj });
      }
      return arr;
    },
    { keys: [] as Array<keyof typeof originEnum>, values: [] as Array<ValueOf<typeof originEnum>> },
  );

  if (keyofEnums.includes('NoTrade')) {
    // logInfo({ keyofEnums, keysLength, lengthAvg, vailedKeys, result });
  }

  return result;
};

export {
  VerifyStatusNum,
  CountryKeyNum,
  CryptoEnum,
  TxCategoryNum,
  TxStatusNum,
  eNumEntities,
  TradeStatusEnum,
  TxStatusMerchantOrder,
  TxTransactionTypeMCOrder,
  TronMasterStatusEnum,
  DIOrderTypeEnum,
  ProxyOrderStatusEnum,
  LedgerSourceEnum,
  LedgerTransactionTypeEnum,
};
