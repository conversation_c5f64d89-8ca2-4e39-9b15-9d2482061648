/* eslint-disable no-console */
type LogProps = unknown;

const logInfo = (props: LogProps, deep?: number) => {
  if (!import.meta.env.DEV || (deep && deep > 5)) return;
  if (['string', 'number'].includes(typeof props)) {
    console.info(`${'-'.repeat((deep ?? 0) * 2)}(${props})`);
    return;
  }
  if (Array.isArray(props)) {
    console.info(`${'-'.repeat((deep ?? 0) * 2)}=== Array ===`);
    logInfo(props.at(0), deep ? deep + 1 : 1);
    return;
  }
  if (typeof props === 'object' && props) {
    Object.entries(props).forEach(([key, value]) => {
      console.info(`${'-'.repeat((deep ?? 0) * 2)}[${key}]: `);
      logInfo(value, deep ? deep + 1 : 1);
    });
    return;
  }
  console.info('Result:', props);
};

const logWarn = (props: LogProps, deep?: number) => {
  if (!import.meta.env.DEV) return;
  if (['string', 'number'].includes(typeof props)) {
    console.warn(`${'-'.repeat((deep ?? 0) * 2)}(${props})`);
    return;
  }
  if (Array.isArray(props)) {
    console.warn(`${'-'.repeat((deep ?? 0) * 2)}=== Array ===`);
    logWarn(props.at(0), deep ? deep + 1 : 1);
    return;
  }
  if (typeof props === 'object' && props) {
    Object.entries(props).forEach(([key, value]) => {
      console.warn(`${'-'.repeat((deep ?? 0) * 2)}[${key}]: `);
      logWarn(value, deep ? deep + 1 : deep);
    });
    return;
  }
  console.warn('Result:', props);
};

export { logInfo, logWarn };
