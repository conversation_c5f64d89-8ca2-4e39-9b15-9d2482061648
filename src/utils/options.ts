import {
  CountryKeyNum,
  CryptoEnum,
  DIOrderTypeEnum,
  eNumEntities,
  LedgerSourceEnum,
  LedgerTransactionTypeEnum,
  ProxyOrderStatusEnum,
  TradeStatusEnum,
  TronMasterStatusEnum,
  TxCategoryNum,
  TxStatusMerchantOrder,
  TxStatusNum,
  VerifyStatusNum,
} from './enums';

type EnumObjBase<T extends Record<string, string | number>, Other = object> = Record<
  keyof T,
  { label: string; value: ValueOf<T> } & Other
>;

const verifyStatusOptions = [
  { value: VerifyStatusNum.Verifing, label: '驗證中' },
  { value: VerifyStatusNum.Fail, label: '失敗' },
  { value: VerifyStatusNum.Pass, label: '通過' },
];

const countryOptions = [
  { value: CountryKeyNum.Taiwan, label: '台灣' },
  { value: CountryKeyNum.Honkon, label: '香港' },
];

const cryptoEnumObj: EnumObjBase<typeof CryptoEnum, { cryptoCurrency: CryptoTypes }> = {
  TRC20_USDT: {
    label: 'TRC20 USDT',
    value: CryptoEnum.TRC20_USDT,
    cryptoCurrency: 'USDT',
  },
  ERC20_USDT: {
    label: 'ERC20 USDT',
    value: CryptoEnum.ERC20_USDT,
    cryptoCurrency: 'USDT',
  },
};

const txStatusEnumObj: EnumObjBase<typeof TxStatusNum, {}> = {
  Created: {
    label: 'txStatusCreated',
    value: TxStatusNum.Created,
  },
  Broadcasted: {
    label: 'txStatusBroadcasted',
    value: TxStatusNum.Broadcasted,
  },
  Confirmed: {
    label: 'txStatusConfirmed',
    value: TxStatusNum.Confirmed,
  },
  Completed: {
    label: 'txStatusCompleted',
    value: TxStatusNum.Completed,
  },
  Canceled: {
    label: 'txStatusCanceled',
    value: TxStatusNum.Canceled,
  },
  Timeout: {
    label: 'txStatusTimeout',
    value: TxStatusNum.Timeout,
  },
  Retry: {
    label: 'txStatusRetry',
    value: TxStatusNum.Retry,
  },
  MerchantCallbackFailed: {
    label: 'txStatusCallbackFailed',
    value: TxStatusNum.MerchantCallbackFailed,
  },
  BlockchainTransactionFailed: {
    label: 'txStatusTransactionFailed',
    value: TxStatusNum.BlockchainTransactionFailed,
  },
};

const txStatusMcOrderEnumObj: EnumObjBase<typeof TxStatusMerchantOrder, {}> = {
  Retry: {
    label: 'retryTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.Retry,
  },
  Created: {
    label: 'createdTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.Created,
  },
  BlockchainBroadcast: {
    label: 'blockchainBroadcastTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.BlockchainBroadcast,
  },
  BlockchainConfirmed: {
    label: 'blockchainConfirmedTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.BlockchainConfirmed,
  },
  Completed: {
    label: 'completedTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.Completed,
  },
  Canceled: {
    label: 'canceledTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.Canceled,
  },
  Timeout: {
    label: 'timeoutTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.Timeout,
  },
  CallbackFailed: {
    label: 'callbackFailedTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.CallbackFailed,
  },
  BlockchainFailed: {
    label: 'blockchainFailedTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.BlockchainFailed,
  },
  OtherError: {
    label: 'otherTxStatusMerchantOrder',
    value: TxStatusMerchantOrder.OtherError,
  },
};

const txCategoryNumObj = {
  Deposit: {
    label: 'depositTxCategoryNum',
    value: TxCategoryNum.Deposit,
  },
  Withdraw: {
    label: 'withdrawTxCategoryNum',
    value: TxCategoryNum.Withdraw,
  },
  Transfer: {
    label: 'transferTxCategoryNum',
    value: TxCategoryNum.Transfer,
  },
  Commission: {
    label: 'commissionTxCategoryNum',
    value: TxCategoryNum.Commission,
  },
};

const txCategoryNumOptions = eNumEntities<typeof TxCategoryNum>(TxCategoryNum).keys.map((enumKey) => {
  const { value = TxCategoryNum[enumKey], label = 'undefined' } = txCategoryNumObj[enumKey];
  return { value, label };
});

const diOrderTypeEnumObj = {
  Buy: {
    label: 'buyDIOrderTypeEnum',
    value: DIOrderTypeEnum.Buy,
  },
  Sell: {
    label: 'sellDIOrderTypeEnum',
    value: DIOrderTypeEnum.Sell,
  },
};

const diOrderTypeEnumOptions = eNumEntities<typeof DIOrderTypeEnum>(DIOrderTypeEnum).keys.map((enumKey) => {
  const { value = DIOrderTypeEnum[enumKey], label = 'undefined' } = diOrderTypeEnumObj[enumKey];
  return { value, label };
});

const proxyOrderStatusEnum: EnumObjBase<typeof ProxyOrderStatusEnum, {}> = {
  Created: {
    label: 'createdProxyOrderStatusEnum',
    value: ProxyOrderStatusEnum.Created,
  },
  Completed: {
    label: 'completedProxyOrderStatusEnum',
    value: ProxyOrderStatusEnum.Completed,
  },
  Cancelled: {
    label: 'cancelledProxyOrderStatusEnum',
    value: ProxyOrderStatusEnum.Cancelled,
  },
  Expired: {
    label: 'expiredProxyOrderStatusEnum',
    value: ProxyOrderStatusEnum.Expired,
  },
};
const proxyOrderStatusOptions = eNumEntities<typeof ProxyOrderStatusEnum>(ProxyOrderStatusEnum).keys.map((enumKey) => {
  const { value = ProxyOrderStatusEnum[enumKey], label = 'undefined' } = proxyOrderStatusEnum[enumKey];
  return { value, label };
});

const ledgerSourceEnumObj: EnumObjBase<typeof LedgerSourceEnum, {}> = {
  ProxyOrder: {
    label: 'proxyOrderLedgerSourceEnum',
    value: LedgerSourceEnum.ProxyOrder,
  },
  MerchantTransaction: {
    label: 'merchantTransactionLedgerSourceEnum',
    value: LedgerSourceEnum.MerchantTransaction,
  },
  MemberTransaction: {
    label: 'memberTransactionLedgerSourceEnum',
    value: LedgerSourceEnum.MemberTransaction,
  },
};
const ledgerSourceEnumOptions = eNumEntities<typeof LedgerSourceEnum>(LedgerSourceEnum).keys.map((enumKey) => {
  const { value = LedgerSourceEnum[enumKey], label = 'undefined' } = ledgerSourceEnumObj[enumKey];
  return { value, label };
});

const ledgerTransactionTypeEnumObj: EnumObjBase<typeof LedgerTransactionTypeEnum, {}> = {
  Buy: {
    label: 'buyLedgerTransactionTypeEnum',
    value: LedgerTransactionTypeEnum.Buy,
  },
  Sell: {
    label: 'sellLedgerTransactionTypeEnum',
    value: LedgerTransactionTypeEnum.Sell,
  },
};
const ledgerTransactionTypeEnumOptions = eNumEntities<typeof LedgerTransactionTypeEnum>(
  LedgerTransactionTypeEnum,
).keys.map((enumKey) => {
  const { value = LedgerTransactionTypeEnum[enumKey], label = 'undefined' } = ledgerTransactionTypeEnumObj[enumKey];
  return { value, label };
});

const tradeStatusEnumObj = {
  Pending: {
    label: 'pendingTradeStatusEnum',
    value: TradeStatusEnum.Pending,
  },
  Accepted: {
    label: 'acceptedTradeStatusEnum',
    value: TradeStatusEnum.Accepted,
  },
  BankAccountAssigned: {
    label: 'bankAccountAssignedTradeStatusEnum',
    value: TradeStatusEnum.BankAccountAssigned,
  },
  PaymentMade: {
    label: 'paymentMadeTradeStatusEnum',
    value: TradeStatusEnum.PaymentMade,
  },
  PaymentConfirmed: {
    label: 'paymentConfirmedTradeStatusEnum',
    value: TradeStatusEnum.PaymentConfirmed,
  },
  Completed: {
    label: 'completedTradeStatusEnum',
    value: TradeStatusEnum.Completed,
  },
  Cancelled: {
    label: 'cancelledTradeStatusEnum',
    value: TradeStatusEnum.Cancelled,
  },
  Expired: {
    label: 'expiredTradeStatusEnum',
    value: TradeStatusEnum.Expired,
  },
};

const txStatusOptions = eNumEntities<typeof TxStatusNum>(TxStatusNum).keys.map((enumKey) => {
  const { value = TxStatusNum[enumKey], label = 'undefined' } = txStatusEnumObj[enumKey];
  return { value, label };
});
const tradeStatusOptions = eNumEntities<typeof TradeStatusEnum>(TradeStatusEnum).keys.map((enumKey) => {
  const { value = TradeStatusEnum[enumKey], label = 'undefined' } = tradeStatusEnumObj[enumKey];
  return { value, label };
});
const txStatusMcOrderOptions = eNumEntities<typeof TxStatusMerchantOrder>(TxStatusMerchantOrder).keys.map((enumKey) => {
  const { value = TxStatusMerchantOrder[enumKey], label = 'undefined' } = txStatusMcOrderEnumObj[enumKey];
  return { value, label };
});

const cryptoEnumOptions = eNumEntities<typeof CryptoEnum>(CryptoEnum).keys.map((mapKey) => {
  const { value = CryptoEnum[mapKey], label = '未定義' } = cryptoEnumObj[mapKey];
  return { value, label };
});

const getCountryBankOptions = (countyKey: CountryKeyNum | undefined) => {
  if (countyKey === CountryKeyNum.Taiwan)
    return [
      { value: '163', label: '臺灣銀行 Bank of Taiwan (163)' },
      { value: '149', label: '臺灣土地銀行 Land Bank of Taiwan（149）' },
      {
        value: '269',
        label: '合作金庫商業銀行 Taiwan Cooperative Bank（269）',
      },
      { value: '186', label: '第一商業銀行 First Commercial Bank（186）' },
      {
        value: '185',
        label: '華南商業銀行 Hua Nan Commercial Bank, Ltd.（185）',
      },
    ];
  return [];
};

// 將值陣列轉為選項陣列
const valuesToFilter = <V = string>(values: Array<V>, options: Array<{ label: React.ReactNode; value: V }>) => {
  return values.map((value) => {
    const option = options.find((findOption) => findOption.value === value);
    return { text: option?.label, value };
  });
};

// 值從列表中轉為字串
const valueToLabel = <V = string>(value: V, options: Array<{ label: React.ReactNode; value: V }>) => {
  return options.find((findOptions) => findOptions.value === value)?.label;
};

const labelsToFilters = <Label = string>(labels: Array<Label>) => {
  return labels.map((mapL) => ({ text: mapL, value: mapL }));
};

// Tron Master status enum options
const tronMasterStatusObj: EnumObjBase<typeof TronMasterStatusEnum, {}> = {
  Normal: {
    label: 'normalTronMasterStatus',
    value: TronMasterStatusEnum.Normal,
  },
  DepositOnly: {
    label: 'depositOnlyTronMasterStatus',
    value: TronMasterStatusEnum.DepositOnly,
  },
  WithdrawalOnly: {
    label: 'withdrawalOnlyTronMasterStatus',
    value: TronMasterStatusEnum.WithdrawalOnly,
  },
  Suspended: {
    label: 'suspendedTronMasterStatus',
    value: TronMasterStatusEnum.Suspended,
  },
};
const tronMasterStatusOptions = eNumEntities<typeof TronMasterStatusEnum>(TronMasterStatusEnum).keys.map((enumKey) => {
  const { value = TronMasterStatusEnum[enumKey], label = 'undefined' } = tronMasterStatusObj[enumKey];
  return { value, label };
});

export {
  getCountryBankOptions,
  verifyStatusOptions,
  countryOptions,
  labelsToFilters,
  valuesToFilter,
  valueToLabel,
  cryptoEnumOptions,
  txStatusOptions,
  tradeStatusOptions,
  txStatusMcOrderOptions,
  tronMasterStatusOptions,
  txCategoryNumOptions,
  diOrderTypeEnumOptions,
  proxyOrderStatusOptions,
  ledgerSourceEnumOptions,
  ledgerTransactionTypeEnumOptions,
};
