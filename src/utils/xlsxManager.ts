// libs
import * as XLSX from 'xlsx';

type UploadExcelProps = {
  file: File;
  sheetNames: Array<string>;
};

const uploadExcel = <T = unknown>({ file, sheetNames }: UploadExcelProps, onSuccess: (dataArray: Array<T>) => void) => {
  const reader = new FileReader();

  reader.onload = (event) => {
    const { result } = event.target || {};
    if (result && typeof result !== 'string') {
      const data = new Uint8Array(result);
      const workbook = XLSX.read(data, { type: 'array' });
      workbook.SheetNames.forEach((name) => {
        sheetNames.forEach((ReqName) => {
          if (name?.includes(ReqName)) {
            const sheetName = name;
            const worksheet = workbook.Sheets[sheetName];
            const excelData = XLSX.utils.sheet_to_json<T>(worksheet, {
              header: 1,
            });
            onSuccess(excelData);
          }
        });
      });
    }
  };

  reader.readAsArrayBuffer(file);
};

const arrayObjToArraySheet = (arrayObj: Array<object>) => {
  const columns = (() => {
    const firstObject = arrayObj.at(0);
    if (!firstObject) return [];
    return Object.keys(firstObject);
  })();
  const rows = (() => {
    return arrayObj.map((obj) => Object.values(obj));
  })();
  const sheet = [columns, ...rows];

  return sheet;
};

type ExportSheetByArrayProps = {
  arrays: Array<Array<string | number | null>>;
  sheetName: string;
  fileName: string;
  wscols?: Array<{ wch: number }>;
  wsrows?: Array<{ hpt: number }>;
};
const exportSheetByArray = (useProps: ExportSheetByArrayProps) => {
  const { arrays, sheetName, fileName, wscols, wsrows } = useProps;
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(arrays);

  // styles
  worksheet['!cols'] = wscols ?? arrays?.[0]?.map(() => ({ wch: 20 }));
  worksheet['!rows'] = wsrows ?? arrays?.map(() => ({ hpt: 18 }));
  const font = { name: '標楷體', sz: 14 };
  // eslint-disable-next-line no-restricted-syntax
  for (const cell in worksheet) {
    if (cell.startsWith('A') || cell.startsWith('B') || cell.startsWith('C')) {
      worksheet[cell].s = { font };
      if (cell === 'A1') {
        worksheet[cell].s.fill = { bgColor: { rgb: 'FFFF00' } };
      }
    }
  }

  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  XLSX.writeFile(workbook, `${fileName}.xlsx`);
};

const exportSheet = <T extends Record<string, any[]>>(data: T, fileName: string) => {
  const worksheets = Object.entries(data).map(([key, value]) => {
    return { key, sheet: XLSX.utils.json_to_sheet(value) };
  });
  const workbook = XLSX.utils.book_new();
  worksheets.forEach((sheetObj) => {
    XLSX.utils.book_append_sheet(workbook, sheetObj.sheet, sheetObj.key);
  });

  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const excelData = new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  const excelUrl = URL.createObjectURL(excelData);

  const a = document.createElement('a');
  a.href = excelUrl;
  a.download = `${fileName}.xlsx`;
  a.click();
  URL.revokeObjectURL(excelUrl);
};

export { uploadExcel, exportSheet, exportSheetByArray, arrayObjToArraySheet };
export type { UploadExcelProps, ExportSheetByArrayProps };
