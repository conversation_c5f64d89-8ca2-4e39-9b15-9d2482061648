// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type UpdateIpRes = {};
type UpdateIpProps = {
  ipAddress: string;
  newDescription: string;
  operationPassword: string;
};
type Other = {};

const useUpdateIp = (useProps: UseTestMutationProps<UpdateIpRes, UpdateIpProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<UpdateIpRes, UpdateIpProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.put('/V1.0/ip/whitelist/merchant', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allIpWhiteList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useUpdateIp };
export type { UpdateIpRes, UpdateIpProps };
