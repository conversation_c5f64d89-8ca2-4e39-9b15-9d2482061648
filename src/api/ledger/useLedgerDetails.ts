import { useTestQuery, UseTestQueryProps } from '@/hook';
import { CryptoEnum, LedgerSourceEnum, LedgerTransactionTypeEnum, ProxyOrderStatusEnum } from '@/utils';
import { axiosRoot } from '@/utils/axiosRoot';

interface LedgerDetail {
  orderUid: string;
  merchantOrderId: string;
  memberId: string;
  payerBankAccountName: string;
  entryCode: string;
  transactionType: LedgerTransactionTypeEnum;
  cryptoType: CryptoEnum;
  cryptoAmount: number;
  fiatType: string;
  fiatAmount: number;
  status: ProxyOrderStatusEnum;
  createdAt: string;
  isMerchantNotified: boolean;
  totalFee: number;
}
interface LedgerDetailsRes {
  id: number;
  source: LedgerSourceEnum;
  sourceDesc: string;
  detail: LedgerDetail;
}
interface LedgerDetailsProps {
  id: number | undefined;
}
interface Other {}

const useLedgerDetails = (useProps: UseTestQueryProps<Other, LedgerDetailsProps>) => {
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<LedgerDetailsRes, LedgerDetailsProps>({
    ...config,
    queryKey: ['ledger', 'details', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get(`/V1.0/merchantLedger/me/${params?.id}`).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useLedgerDetails };
export type { LedgerDetail, LedgerDetailsRes };
