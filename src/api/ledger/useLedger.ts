import { useTestQuery, UseTestQueryProps } from '@/hook';
import { CryptoEnum, LedgerSourceEnum } from '@/utils';
import { axiosRoot } from '@/utils/axiosRoot';

interface LedgerItemInterface {
  id: number;
  cryptoType: CryptoEnum;
  amount: number;
  balanceAfter: number;
  totalFee: number;
  relatedOrderNo: string;
  source: LedgerSourceEnum;
  sourceDesc: string;
  createdAt: string;
}
interface LedgerListRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<LedgerItemInterface>;
}
interface LedgerListProps {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'Id';
  OrderByDescending?: boolean;
  StartTime?: string;
  EndTime?: string;
}
interface Other {}

const useLedger = (useProps: UseTestQueryProps<Other, LedgerListProps>) => {
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<LedgerListRes, LedgerListProps>({
    ...config,
    queryKey: ['ledger', 'list', ...Object.values(params || {})],
    qf: () => {
      const request = axiosRoot.get('/V1.0/merchantLedger/me', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useLedger };
export type { LedgerItemInterface, LedgerListProps };
