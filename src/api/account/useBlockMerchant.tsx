// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type BlockMerchantRes = {};
type BlockMerchantProps = {
  userId: string;
  operationPassword: string;
};
type Other = {};

const useBlockMerchant = (useProps: UseTestMutationProps<BlockMerchantRes, BlockMerchantProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<BlockMerchantRes, BlockMerchantProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/block/toggle/', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allUserList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useBlockMerchant };
export type { BlockMerchantRes, BlockMerchantProps };
