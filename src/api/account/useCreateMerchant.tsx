// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type CreateMerchantRes = {};
type CreateMerchantProps = {
  userName: string;
  password: string;
  nickName: string;
};
type Other = {};

const useCreateMerchant = (useProps: UseTestMutationProps<CreateMerchantRes, CreateMerchantProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<CreateMerchantRes, CreateMerchantProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/merchant/user', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allUserList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateMerchant };
export type { CreateMerchantRes, CreateMerchantProps };
