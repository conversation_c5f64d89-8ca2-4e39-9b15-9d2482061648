// hooks
import { useTestQuery, UseTestQueryProps } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type McUserListOptions = {
  appUserId: string;
  id: number;
  merchantNumber: number;
  merchantName: number;
  userName: string;
  nickName: string;
  operationPasswordEnabled: boolean;
  twoFactorEnabled: boolean;
  roles: Array<RoleTypes>;
  createdAt: string;
  isLockedOut: boolean;
};

type McUserListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: McUserListOptions[];
};

type McUserListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
  OrderUid?: string;
  AppUserId?: string;
  UserName?: string;
  NickName?: string;
};

type Other = {};
const useMcUserList = (useProps: UseTestQueryProps<Other, McUserListProps>) => {
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<McUserListRes, McUserListProps>({
    ...config,
    queryKey: queryKeys.query.usertList(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/account/merchant/self', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useMcUserList };

export type { McUserListOptions, McUserListRes, McUserListProps };
