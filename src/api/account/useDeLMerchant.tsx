// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type DeLMerchantRes = {};
type DeLMerchantProps = {
  userId: string;
  operationPassword: string;
};
type Other = {};

const useDeLMerchant = (useProps: UseTestMutationProps<DeLMerchantRes, DeLMerchantProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<DeLMerchantRes, DeLMerchantProps>({
    ...config,
    mutationFn: ({ userId, operationPassword }) => {
      const request = axiosRoot
        .delete(`/V1.0/account/${userId}`, {
          data: { operationPassword },
        })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allUserList().forEach((eachMcxKeys) => {
        queryClient.invalidateQueries({ queryKey: eachMcxKeys });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useDeLMerchant };
export type { DeLMerchantRes, DeLMerchantProps };
