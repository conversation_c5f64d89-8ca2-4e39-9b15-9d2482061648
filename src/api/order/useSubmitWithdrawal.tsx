// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type SubmitWithdrawalRes = {};
type SubmitWithdrawalProps = {
  to: string;
  amount: number;
  remark: string;
  operationPassword: string;
};
type Other = {};

const useSubmitWithdrawal = (useProps: UseTestMutationProps<SubmitWithdrawalRes, SubmitWithdrawalProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<SubmitWithdrawalRes, SubmitWithdrawalProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/merchant-order/withdrawal/submit', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryKeys.query.allMerchantOrderList().forEach((key) => {
        queryClient.invalidateQueries({ queryKey: key });
      });

      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSubmitWithdrawal };
export type { SubmitWithdrawalRes, SubmitWithdrawalProps };
