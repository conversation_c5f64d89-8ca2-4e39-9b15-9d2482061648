// hooks
import { useTestQuery, UseTestQueryProps } from '@/hook';

// utils
import { CryptoEnum, TxCategoryNum, TxStatusMerchantOrder } from '@/utils';
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

type McOrderListOptions = {
  id: number;
  merchantNumber: number;
  merchantName: number;
  applicantName: string;
  approverName: string;
  order: {
    requireAmount: number;
    actualAmount: number;
    transactionType: TxCategoryNum;
    cryptoType: CryptoEnum;
    status: TxStatusMerchantOrder;
    remark: string;
    hash: string;
    to: string;
    from: string;
    gas: number;
    fee: number;
    confirmedAt: string;
  };
  createdAt: string;
};

type McOrderListRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: McOrderListOptions[];
};

type McOrderListProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: number;
  OrderByDescending?: number;
  TransactionHash?: string;
  TransactionType?: TxCategoryNum;
  Status?: Array<TxStatusMerchantOrder>;
  ConfirmedAtStart?: number;
  ConfirmedAtEnd?: number;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
  MerchantNumber?: string;
  MerchantName?: string;
  CryptoType?: CryptoEnum;
};
type Other = {};
const useMcOrderList = (useProps: UseTestQueryProps<Other, McOrderListProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  // compute
  const statusQuery = params?.Status?.map((mapS) => `Status=${mapS}`).join('&') || '';

  const testQuery = useTestQuery<McOrderListRes, McOrderListProps>({
    ...config,
    queryKey: queryKeys.query.merchantOrderList(params),
    qf: () => {
      const request = axiosRoot
        .get(`/V1.0/merchant-order/me?${statusQuery}`, { params: { ...params, Status: undefined } })
        .then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 5000,
  });

  return testQuery;
};

export { useMcOrderList };

export type { McOrderListOptions, McOrderListRes, McOrderListProps };
