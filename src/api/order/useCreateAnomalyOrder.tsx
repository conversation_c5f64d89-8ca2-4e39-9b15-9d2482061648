// libs
import { v4 as uuidv4 } from 'uuid';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

type CreateAnomalyOrderlRes = {};
type CreateAnomalyOrderProps = {
  orderNumber: string;
  hash: string;
  remark: string;
  operationPassword: string;
};
type Other = {};

const useCreateAnomalyOrder = (
  useProps: UseTestMutationProps<CreateAnomalyOrderlRes, CreateAnomalyOrderProps, Other>,
) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<CreateAnomalyOrderlRes, CreateAnomalyOrderProps>({
    ...config,
    mutationFn: (props) => {
      const orderNumber = uuidv4();
      const requestData = { ...props, orderNumber };
      const request = axiosRoot.post('/V1.0/depositAnomaly', requestData).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useCreateAnomalyOrder };
export type { CreateAnomalyOrderlRes, CreateAnomalyOrderProps };
