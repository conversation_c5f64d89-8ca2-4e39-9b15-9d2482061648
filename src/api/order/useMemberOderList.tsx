// hooks
import { useTestQuery, UseTestQueryProps } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';
import { CryptoEnum, TxCategoryNum, TxStatusNum } from '@/utils';

type OderListMerchantOptions = {
  orderUid: string;
  createdAt: string;
  merchantOrderId: string;
  merchantId: number;
  merchantNumber: string;
  merchantName: string;
  merchantUserName: string;
  order: {
    requireAmount: number;
    actualAmount: number;
    transactionType: TxCategoryNum;
    cryptoType: CryptoEnum;
    status: TxStatusNum;
    remark: string;
    hash: string;
    to: string;
    from: string | null;
    gas: number;
    fee: number;
    confirmedAt: string | null;
  };
};

type OderListMerchantOptionsRes = {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: OderListMerchantOptions[];
};

type OderListMerchantOptionsProps = {
  PageNumber?: number;
  PageSize?: number;
  OrderBy?: 'ActualAmount' | 'Gas' | 'Fee' | 'Id';
  OrderByDescending?: boolean;
  OrderUid?: string;
  MerchantOrderId?: string;
  SystemOrderId?: string;
  MerchantNumber?: string;
  Hash?: string;
  CryptoType?: CryptoEnum;
  TransactionType?: TxCategoryNum;
  Status?: Array<TxStatusNum>;
  ConfirmedAtStart?: number;
  ConfirmedAtEnd?: number;
  CreatedAtStart?: string;
  CreatedAtEnd?: string;
  IsManualOrder?: number;
  IsWithdrawalFail?: number;
  IsUncompleted?: boolean;
};

type Other = {};
const useMemberOderList = (useProps: UseTestQueryProps<Other, OderListMerchantOptionsProps>) => {
  // props
  const { params, onSuccess, ...config } = useProps;

  const testQuery = useTestQuery<OderListMerchantOptionsRes, OderListMerchantOptionsProps>({
    ...config,
    queryKey: queryKeys.query.orderList(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/order/me', { params }).then(({ data }) => data);

      return request;
    },
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res, params);
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useMemberOderList };

export type { OderListMerchantOptions, OderListMerchantOptionsRes, OderListMerchantOptionsProps };
