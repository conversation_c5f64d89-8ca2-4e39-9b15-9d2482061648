// libs
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

// store
import { useUserStore, useNotifyStore } from '@/store';

// api
import { LoginRes } from './useLogin';

type Vfy2FaRes = LoginRes;
type Vfy2FaProps = {
  TwoFactorCode: string;
};
type Other = {};

const useVfy2Fa = (useProps: UseTestMutationProps<Vfy2FaRes, Vfy2FaProps, Other>) => {
  // props
  const { onSuccess, onError, ...config } = useProps;

  // hooks
  const navigate = useNavigate();
  const { setLoginProps, setLoginRes } = useUserStore();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('useVfy2Fa');

  const testMutation = useTestMutation<Vfy2FaRes, Vfy2FaProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/auth/login/2fa', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);

      setLoginRes(res);
      pushBSQ([{ title: 'UXM office', des: t('successDescription') }]);
      setTimeout(() => {
        navigate('/');
      }, 100);
    },
    onError: (error) => {
      if (onError) onError(error);
      setLoginProps(null);
      setLoginRes(null);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useVfy2Fa };
export type { Vfy2FaRes, Vfy2FaProps };
