// hooks
import { UseTestQueryProps, useTestQuery } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

// store
import { useUserStore } from '@/store/useUserStore';

type InfoRes = {
  userId: string;
  userName: string;
  nickName: string;
  operationPasswordEnabled: boolean;
  twoFactorEnabled: boolean;
  roles: Array<RoleTypes>;
};
type InfoProps = {};
type Other = {};

const useInfo = (useProps: UseTestQueryProps<Other, InfoProps>) => {
  const { onSuccess, onError, ...config } = useProps;
  const { loginRes, setInfo, setLoginRes } = useUserStore();

  const testQuery = useTestQuery<InfoRes, InfoProps>({
    ...config,
    queryKey: queryKeys.query.info(loginRes?.token),
    qf: (params) => {
      const request = axiosRoot.get('/V1.0/auth/me', { params }).then(({ data }) => data);

      return request;
    },
    delay: 1000,
    enabled: !!loginRes?.token,
    onSuccess: (res) => {
      if (onSuccess) onSuccess(res);
      setInfo(res);
    },
    onError: (error) => {
      if (onError) onError(error);
      setLoginRes(null);
    },
    gcTime: 1000 * 60 * 60,
    staleTime: 1000 * 60 * 60,
  });

  return testQuery;
};

export { useInfo };
export type { InfoRes, InfoProps };
