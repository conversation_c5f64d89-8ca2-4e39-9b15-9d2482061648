// libs
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

// store
import { useUserStore, useNotifyStore } from '@/store';

type LoginRes = {
  id: string;
  requiredTwoFactor: boolean;
  userName: string;
  token: string;
  roles: Array<RoleTypes>;
};

type LoginProps = {
  merchantNumber: string;
  username: string;
  password: string;
};
type Other = {};

const useLogin = (useProps: UseTestMutationProps<LoginRes, LoginProps, Other>) => {
  // props
  const { onError, onSuccess, ...config } = useProps;

  // hooks
  const navigate = useNavigate();
  const { setLoginProps, setLoginRes } = useUserStore();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('useLogin');

  const testMutation = useTestMutation<LoginRes, LoginProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/auth/merchant/login', props).then(({ data }) => data);

      return request;
    },
    onMutate: (props) => {
      setLoginProps(props);
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      if (res.requiredTwoFactor) {
        pushBSQ([{ title: 'UXM client', des: t('require2FADescription') }]);

        return;
      }
      setLoginRes(res);
      pushBSQ([{ title: 'UXM client', des: t('loginSuccessDescription') }]);
      setTimeout(() => {
        navigate('/');
      }, 100);
    },
    onError: (error) => {
      if (onError) onError(error);
      setLoginProps(null);
      setLoginRes(null);
    },
    delay: 1000,
  });

  return testMutation;
};

export { useLogin };
export type { LoginProps, LoginRes };
