// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

type LgChangepwRes = {};
type LLgChangepwProps = {
  currentPassword: string;
  newPassword: string;
};
type Other = {};

const useChangePWLogin = (useProps: UseTestMutationProps<LgChangepwRes, LLgChangepwProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<LgChangepwRes, LLgChangepwProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/account/login-password/change', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useChangePWLogin };
export type { LgChangepwRes, LLgChangepwProps };
