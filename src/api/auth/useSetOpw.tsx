// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

type SetOpwRes = {};
type SetOpwProps = {
  loginPassword: string;
  operationPassword: string;
};
type Other = {};

const useSetOpw = (useProps: UseTestMutationProps<SetOpwRes, SetOpwProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<SetOpwRes, SetOpwProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/v1/Account/operation-password/set', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useSetOpw };
export type { SetOpwRes, SetOpwProps };
