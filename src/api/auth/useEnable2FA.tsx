// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

type Enable2FARes = {};
type Enable2FAProps = {
  FirstCode: string;
  SecondCode: string;
};
type Other = {};

const useEnable2FA = (useProps: UseTestMutationProps<Enable2FARes, Enable2FAProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<Enable2FARes, Enable2FAProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/twoFactor/enable', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });
  return testMutation;
};

export { useEnable2FA };
export type { Enable2FARes, Enable2FAProps };
