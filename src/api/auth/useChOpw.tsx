// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';

type ChOpwRes = {};
type ChOpwProps = {
  oldOperationPassword: string;
  newOperationPassword: string;
};
type Other = {};

const useChOpw = (useProps: UseTestMutationProps<ChOpwRes, ChOpwProps, Other>) => {
  // props
  const { ...config } = useProps;

  const testMutation = useTestMutation<ChOpwRes, ChOpwProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/v1/Account/operation-password/change', props).then(({ data }) => data);

      return request;
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useChOpw };
export type { ChOpwRes, ChOpwProps };
