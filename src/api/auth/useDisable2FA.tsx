// libs
import { useQueryClient } from '@tanstack/react-query';

// hooks
import { UseTestMutationProps, useTestMutation } from '@/hook';

// utils
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

// store
import { useUserStore } from '@/store';

type Disable2FARes = {};
type Disable2FAProps = {
  OperationPassword?: string;
};
type Other = {};

const useDisable2FA = (useProps: UseTestMutationProps<Disable2FARes, Disable2FAProps, Other>) => {
  // props
  const { onSuccess, ...config } = useProps;

  // hooks
  const { loginRes } = useUserStore();
  const queryClient = useQueryClient();

  const testMutation = useTestMutation<Disable2FARes, Disable2FAProps>({
    ...config,
    mutationFn: (props) => {
      const request = axiosRoot.post('/V1.0/twoFactor/disable', props).then(({ data }) => data);

      return request;
    },
    onSuccess: (res, params) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.query.info(loginRes?.token) });
      if (onSuccess) onSuccess(res, params);
    },
    skipLogger: true,
  });

  return testMutation;
};

export { useDisable2FA };
export type { Disable2FARes, Disable2FAProps };
