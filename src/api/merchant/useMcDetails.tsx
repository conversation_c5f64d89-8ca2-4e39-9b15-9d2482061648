// hooks
import { useTestQuery, UseTestQueryProps } from '@/hook';

// utils
import { CryptoEnum } from '@/utils';
import { axiosRoot } from '@/utils/axiosRoot';
import queryKeys from '@/utils/queryKeys';

interface MerchantWallet {
  balance: number;
  lockedBalance: number;
}

interface MerchantMaster {
  allowedDepositTolerance: number;
  depositTimeoutMinutes: number;
  maxMerchantUsers: number;
  maxPendingDepositOrders: number;
  maxUserPendingDepositOrders: number;
}

interface MerchantFee {
  cryptoType: CryptoEnum;
  fixedFee: number;
  percentageFee: number;
  depositFee: number;
  withdrawalFee: number;
}

interface MerchantPaymentAddress {
  networkType: CryptoEnum;
  addresses: string;
}

interface MerchantReceiveAddress {
  networkType: CryptoEnum;
  addresses: string;
}

interface MerchantIpWhitelist {
  enableIpWhitelist: boolean;
  ipWhitelistCount: number;
}

type MerchantListProps = {};
type MerchantOptions = {
  id: number;
  merchantNumber: string;
  merchantName: string;
  customerName: string;
  enabled: boolean;
  transactionStatus: number;
  isOrderLimitReached: false;
  pendingDepositCount: number;
  userCount: number;
  ipWhiteList?: MerchantIpWhitelist;
  wallet?: MerchantWallet;
  master?: MerchantMaster;
  fee?: MerchantFee[];
  paymentAddress?: MerchantPaymentAddress[];
  receiveAddress?: MerchantReceiveAddress[];
  createdAt: string;
  updatedAt: string;
};

type Other = {};

const useMcDetails = (useProps: UseTestQueryProps<Other, MerchantListProps>) => {
  // props
  const { params, ...config } = useProps;

  const testQuery = useTestQuery<MerchantOptions, MerchantListProps>({
    ...config,
    queryKey: queryKeys.query.merchantDetailBalance(params),
    qf: () => {
      const request = axiosRoot.get('/V1.0/merchant/details/self', { params }).then(({ data }) => data);

      return request;
    },
    staleTime: 1000 * 5,
  });

  return testQuery;
};

export { useMcDetails };
export type {
  MerchantReceiveAddress,
  MerchantListProps,
  MerchantOptions,
  MerchantWallet,
  MerchantFee,
  MerchantPaymentAddress,
};
