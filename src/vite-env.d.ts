/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_AXIOS_ROOT: string;
  readonly VITE_HUBS_ROOT: string;
  readonly VITE_VERSION: string;
  readonly VITE_DI_URL: string;
}

type ValueOf<T> = T[keyof T];

enum MikeErrorCodeNum {
  'TestCode' = -1,
  'VerifyError' = 0,
  'Nothing' = 1,
  'CantSms' = 4,
}

interface IMikeRes<DATA = unknown> {
  status: number;
  data: DATA;
  msg: string;
}

interface Window {
  myGlobalVariable?: {
    dxRq?: Array<{
      temp: Object;
      key: Array<string>; // userId, pageSize, depth
    }>;
    chatHubConnection?: Object;
    orderListKeys?: Array<Array<string | number | null | undefined | boolean>>;
    merchantOrderListKeys?: Array<Array<string | number | null | undefined | boolean>>;
    balanceKeys?: Array<Array<string | number | null | undefined | boolean>>;
    userListKeys?: Array<Array<string | number>>;
    ipWhiteListKeys?: Array<Array<string | number>>;
    diOrderListKeys?: Array<Array<string | number | null | undefined | boolean>>;
  };
}

type NOU = null | undefined;
type ReactSet<T> = React.Dispatch<React.SetStateAction<T>>;
interface IMikeError {
  errorCode: MikeErrorCodeNum;
  errors: null | Object;
  status: number;
  title: string;
  type: string;
}

type RequestOptions = IMikeRes;
type RequestError = IMikeError;
type RoleTypes = 'MerchantAdmin' | 'MerchantCs';
type LocaleTypes = 'zh-TW' | 'zh-CN' | 'en-US' | 'vi-VN';
type CryptoTypes = 'USDT' | 'BandWidth';
type ProtoColTypes = 'TRC-20' | 'ERC-20';
