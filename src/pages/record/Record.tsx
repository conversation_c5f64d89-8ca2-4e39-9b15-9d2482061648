// libs
import * as React from 'react';
import { TabsProps, Tabs, Breadcrumb, Card } from 'antd';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// pages
import AccountJournal from './components/AccountJournal';

interface IRecordProps {}

const Record: React.FunctionComponent<IRecordProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [activeKey, setActiveKey] = React.useState<string>();

  // hooks
  const navigator = useNavigate();
  const location = useLocation();
  const { t } = useTranslation('record');

  // compute
  const items: TabsProps['items'] = React.useMemo(() => {
    return [
      {
        key: 'merchantAccountBook',
        label: t('label'),
        children: <AccountJournal />,
      },
    ];
  }, [t]);

  // === init ===
  React.useEffect(() => {
    if (activeKey !== undefined || !items.length) return;
    if (location.state?.tgTabKey) setActiveKey(location.state?.tgTabKey);
    else setActiveKey(items.at(0)?.key);
  }, [activeKey, items, location.state?.tgTabKey]);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Card className='my-5'>
        <Tabs
          {...{ activeKey, items }}
          indicator={{ size: (origin) => origin - 20 }}
          onChange={(newActiveKey) => {
            setActiveKey(newActiveKey);
            navigator('.', { state: { tgTabKey: newActiveKey } });
          }}
        />
      </Card>
    </div>
  );
};

export default Record;
