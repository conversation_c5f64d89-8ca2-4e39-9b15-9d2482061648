import * as React from 'react';
import { useState } from 'react';
import { <PERSON>readcrumb, Flex, Select } from 'antd';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { OderListMerchantOptions, OderListMerchantOptionsProps, useMemberOderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import BtnFuncs from '@/components/BtnFuncs';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import {
  CryptoEnum,
  cryptoEnumOptions,
  exportSheetByArray,
  storageHelper,
  txCategoryNumOptions,
  TxStatusNum,
  txStatusOptions,
} from '@/utils';
import TransferCreate from '@/pages/wallet/components/TransferCreate';
import OrderDetails from '@/pages/OrderDetails';
import { Txt } from '@/components/TypographyMaster';
import useAccountJournalColumns from './useAccountJournalColumns';

interface IWalletProps {
  isActive?: boolean;
}

const AccountJournal: React.FunctionComponent<IWalletProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const storageRange = storageHelper<{ from: string; to: string }>('walletRange').getItem();
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const { t } = useTranslation('accountJournal');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'accountJournal' });

  // states
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [openTransferCreate, setOpenTransferCreate] = useState(false);
  const [orderMatchFrom, setOrdertMatchFrom] = useState<OderListMerchantOptions>();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const [OrderUid, setOrderUid] = useState<string>();
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [Hash, setHash] = useState<string>();
  const [Status, setStatus] = useState<Array<TxStatusNum>>();
  const [CryptoType, setCryptoType] = useState<CryptoEnum>();
  const [OrderBy, setOrderBy] = useState<OderListMerchantOptionsProps['OrderBy']>();
  const [OrderByDescending, setOrderByDescending] = useState<boolean>();

  const { data, isPending, isRefetching } = useMemberOderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      OrderUid,
      MerchantOrderId,
      Hash,
      CryptoType,
      Status,
      OrderByDescending,
      OrderBy,
    },
  });

  const dataSource = React.useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  const { columns } = useAccountJournalColumns({ data, setOrdertMatchFrom });

  const translateTxStatusOptions = React.useMemo(() => {
    return txStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) }));
  }, [optionsT]);

  const handleOnDateSubmit = React.useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('walletRange').setItem(newDate);
  }, []);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex
        justify='space-between'
        className='mb-3 mt-10 flex-wrap gap-2'
      >
        <div className='text-xl'>{t('title')}</div>
        <Flex
          gap={10}
          className='items-center'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
          <BtnFuncs
            iconType='print'
            loading={isRefetching}
            onClick={() => {
              const sheetTitleRow = [
                'merchantNumberTitleRow',
                'merchantNameTitleRow',
                'typeTitleRow',
                'createdAtTitleRow',
                'confirmedAtTitleRow',
                'orderUidTitleRow',
                'merchantOrderNumberTitleRow',
                'transactionHashTitleRow',
                'fromAddressTitleRow',
                'toAddressTitleRow',
                'cryptoTitleRow',
                'consumedTitleRow',
                'feeTitleRow',
                'requiredTitleRow',
                'actualTitleRow',
                'statusTitleRow',
              ];
              const sheetDataRows = [
                ...dataSource.map((record) => {
                  const transactionType = txCategoryNumOptions.find(
                    (findO) => findO.value === record.order.transactionType,
                  );
                  const cryptoType = cryptoEnumOptions.find((findO) => findO.value === record.order.cryptoType);
                  const statusOption = txStatusOptions.find((findO) => findO.value === record.order.status);
                  const createdAt = record.createdAt ? dayjs(record.createdAt).format(dateFormator.accurate) : '';
                  const confirmedAt = record.order.confirmedAt
                    ? dayjs(record.order.confirmedAt).format(dateFormator.accurate)
                    : '';
                  return [
                    record.merchantNumber,
                    record.merchantName,
                    optionsT(transactionType?.label || 'undefined'),
                    createdAt,
                    confirmedAt,
                    record.orderUid,
                    record.merchantOrderId,
                    record.order.hash,
                    record.order.from,
                    record.order.to,
                    optionsT(cryptoType?.label || 'undefined'),
                    record.order.gas,
                    record.order.fee,
                    record.order.requireAmount,
                    record.order.actualAmount,
                    optionsT(statusOption?.label || 'undefined'),
                  ];
                }),
              ];

              exportSheetByArray({
                arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
                sheetName: t('filename'),
                fileName: `${t('fileName')} ${dayjs().format(dateFormator.accurate)}`,
              });
            }}
          />
        </Flex>
      </Flex>

      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='orderUid'
        loading={isPending}
        onFilterChange={(filters) => {
          if ('state' in filters) {
            setStatus(filters.state as unknown as typeof Status);
          }
          if ('crypto' in filters) {
            setCryptoType(filters.crypto.at(0) as typeof CryptoType);
          }
        }}
        onChange={(_, _filters, sortResults) => {
          const firstSortResults = (() => {
            if (Array.isArray(sortResults)) return sortResults.at(0);
            return sortResults;
          })();
          const { order: orderOrderType, columnKey } = firstSortResults || {};
          setOrderByDescending(() => {
            if (orderOrderType === undefined) return orderOrderType;
            return orderOrderType === 'descend';
          });
          setOrderBy(() => {
            if (columnKey === 'actualAmount') return 'ActualAmount';
            return undefined;
          });
        }}
        titleRender={
          <div className='flex flex-wrap items-center gap-x-2'>
            <div className='flex flex-col gap-y-1'>
              <Txt
                type='secondary'
                className='font-bold'
              >
                {t('statusLabel')}:
              </Txt>
              <Select
                placeholder={t('statusPlaceholder')}
                variant='filled'
                className='w-[160px]'
                options={translateTxStatusOptions}
                disabled={isRefetching}
                onChange={(newValue) => setStatus(newValue)}
                allowClear
              />
            </div>
            <SearchMaster
              titles={[
                { key: 'OrderUid', label: t('orderUidLabel') },
                { key: 'MerchantOrderId', label: t('merchantOrderIdLabel') },
                { key: 'Hash', label: t('hashLabel') },
              ]}
              onSearch={(values) => {
                setOrderUid(values.OrderUid || undefined);
                setMerchantOrderId(values.MerchantOrderId || undefined);
                setHash(values.Hash || undefined);
              }}
            />
          </div>
        }
      />
      <OrderDetails
        order={orderMatchFrom}
        setOrder={setOrdertMatchFrom}
      />
      <TransferCreate
        open={openTransferCreate}
        onCancel={() => setOpenTransferCreate(false)}
      />
    </div>
  );
};

export default AccountJournal;
