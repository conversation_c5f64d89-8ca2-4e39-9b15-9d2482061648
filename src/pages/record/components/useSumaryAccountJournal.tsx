// libs
import { Table, TableProps } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { McOrderListOptions } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

type SumaryAccountJournalProps = {
  columns: TableProps<McOrderListOptions>['columns'];
};
const useSumaryAccountJournal = (props: SumaryAccountJournalProps) => {
  // props
  const { columns } = props || {};

  // hooks
  const { t } = useTranslation('useSumaryAccountJournal');

  // compute
  const summary = useCallback(
    (pageData: readonly McOrderListOptions[]) => {
      const summrayData = pageData;
      const totalamount = summrayData.reduce((preValue, current) => preValue + current.order.actualAmount, 0);
      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns?.map((mapC, index) => {
              const key = `${mapC.key}-${index}`;

              if (mapC.key === 'fee')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-left font-bold'
                  >
                    <Txt>{t('subtotal')}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'actualAmount')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <Txt> {nTot({ value: totalamount })}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'state') return undefined;
              return (
                <Table.Summary.Cell
                  key={key}
                  index={index}
                />
              );
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns, t],
  );

  return summary;
};

export default useSumaryAccountJournal;
