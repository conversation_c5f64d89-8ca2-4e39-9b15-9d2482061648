import { <PERSON><PERSON><PERSON><PERSON>b, Flex, Card, Tabs, TabsProps, Alert, Select } from 'antd';
import * as React from 'react';
import dayjs from 'dayjs';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMcDetails, useMcOrderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import BtnFuncs from '@/components/BtnFuncs';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import {
  CryptoEnum,
  cryptoEnumOptions,
  exportSheetByArray,
  storageHelper,
  txCategoryNumOptions,
  txStatusMcOrderOptions,
  TxStatusMerchantOrder,
} from '@/utils';
import { useThemeStore } from '@/store/useThemeStore';
import useWalletMerchantColumns from './useWalletMerchantColumns';
import TransferCreate from './components/TransferCreate';
import PaymentAddress from './components/PaymentAddress';

interface IWalletProps {
  active?: boolean;
}

const Wallet: React.FunctionComponent<IWalletProps> = (props) => {
  // props
  const { active } = props || {};

  // hooks
  const storageRange = storageHelper<{ from: string; to: string }>('walletRange').getItem();
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const { isWhite } = useThemeStore();
  const navigator = useNavigate();
  const location = useLocation();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { t } = useTranslation('wallet');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'wallet' });

  // states
  const [openTransferCreate, setOpenTransferCreate] = useState(false);
  const [activeKey, setActiveKey] = React.useState<string>();
  const [cryptoType, setCryptoType] = useState<CryptoEnum>();
  const [TransactionHash, setTransactionHash] = useState<string>();
  const [Status, setStatus] = useState<Array<TxStatusMerchantOrder>>();

  const { data, isPending, isRefetching } = useMcOrderList({
    enabled: active,
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      CryptoType: cryptoType,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      TransactionHash,
      Status,
    },
  });

  // merchant details
  const { data: isPaymentAd, isPending: isLoad, isRefetching: isRef } = useMcDetails({});

  const address1 = isPaymentAd?.paymentAddress?.[0]?.addresses;
  const netwokType = isPaymentAd?.paymentAddress?.[0]?.networkType;

  const dataSource = React.useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  const { columns } = useWalletMerchantColumns({});

  const handleOnDateSubmit = React.useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('walletRange').setItem(newDate);
  }, []);

  const items: TabsProps['items'] = React.useMemo(() => {
    return [
      {
        key: 'TRC20錢包',
        label: t('trc20Label'),
        children: (
          <PaymentAddress
            {...{ isWhite }}
            walletType={netwokType ?? CryptoEnum.TRC20_USDT}
            walletAddress={address1 || '-'}
            supportedCurrencies={['USDT', 'TRX']}
            loading={isLoad || isRef}
            onTransferClick={() => setOpenTransferCreate(true)}
          />
        ),
      },
    ];
  }, [address1, isLoad, isRef, isWhite, netwokType, t]);

  // === init ===
  React.useEffect(() => {
    if (activeKey !== undefined || !items.length) return;
    if (location.state?.tgTabKey) setActiveKey(location.state?.tgTabKey);
    else setActiveKey(items.at(0)?.key);
  }, [activeKey, items, location.state?.tgTabKey]);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Card
        className='my-5'
        title={t('title')}
      >
        <Tabs
          {...{ activeKey, items }}
          indicator={{ size: (origin) => origin - 20 }}
          onChange={(newActiveKey) => {
            setActiveKey(newActiveKey);
            navigator('.', { state: { tgTabKey: newActiveKey } });
          }}
        />
        <Alert
          className='mt-5'
          description={t('alertMessage')}
          type='warning'
          showIcon
        />
      </Card>
      <Flex
        justify='space-between'
        className='mb-3 mt-10'
      >
        <Flex
          gap={10}
          className='flex-wrap items-center gap-2'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
          <Select
            placeholder={t('currencyPlaceholder')}
            variant='filled'
            className='w-[160px]'
            options={cryptoEnumOptions}
            disabled={isRefetching}
            onChange={(newValue) => setCryptoType(newValue)}
            allowClear
          />
        </Flex>
        <BtnFuncs
          iconType='print'
          loading={isRefetching}
          onClick={() => {
            const sheetTitleRow = [
              'typeTitleRow',
              'merchantNumberTitleRow',
              'merchantNameTitleRow',
              'createdAtTitleRow',
              'confirmedAtTitleRow',
              'applicantTitleRow',
              'approverTitleRow',
              'transactionHashTitleRow',
              'fromAddressTitleRow',
              'toAddressTitleRow',
              'cryptoTitleRow',
              'consumedTitleRow',
              'feeTitleRow',
              'requiredTitleRow',
              'actualTitleRow',
              'statusTitleRow',
              'remarkTitleRow',
            ];
            const sheetDataRows = [
              ...dataSource.map((item) => {
                const transactionType = txCategoryNumOptions.find(
                  (findO) => findO.value === item.order.transactionType,
                );
                const crypto = cryptoEnumOptions.find((findO) => findO.value === item.order.cryptoType);
                const statusOption = txStatusMcOrderOptions.find((findO) => findO.value === item.order.status);
                const createdAt = item.createdAt ? dayjs(item.createdAt).format(dateFormator.accurate) : '';
                const confirmedAt = item.order.confirmedAt
                  ? dayjs(item.order.confirmedAt).format(dateFormator.accurate)
                  : '';
                return [
                  optionsT(transactionType?.label || 'undefined'),
                  item.merchantNumber,
                  item.merchantName,
                  createdAt,
                  confirmedAt,
                  item.applicantName,
                  item.approverName,
                  item.order.hash,
                  item.order.from,
                  item.order.to,
                  optionsT(crypto?.label || 'undefined'),
                  item.order.gas,
                  item.order.fee,
                  item.order.requireAmount,
                  item.order.actualAmount,
                  optionsT(statusOption?.label || 'undefined'),
                  item.order.remark,
                ];
              }),
            ];

            exportSheetByArray({
              arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
              sheetName: t('filename'),
              fileName: `${t('fileName')} ${dayjs().format(dateFormator.accurate)}`,
            });
          }}
        />
      </Flex>

      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='id'
        onFilterChange={(filters) => {
          if ('state' in filters) {
            setStatus(filters.state as unknown as typeof Status);
          }
        }}
        titleRender={
          <div className='flex items-center space-x-5'>
            <SearchMaster
              titles={[{ key: 'TransactionHash', label: t('hashLabel') }]}
              onSearch={(values) => {
                setTransactionHash(values.TransactionHash || undefined);
              }}
            />
          </div>
        }
      />
      <TransferCreate
        open={openTransferCreate}
        onCancel={() => setOpenTransferCreate(false)}
      />
    </div>
  );
};

export default Wallet;
