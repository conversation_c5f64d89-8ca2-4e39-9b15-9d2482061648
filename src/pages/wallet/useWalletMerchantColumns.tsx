// libs
import { useState } from 'react';
import { TableColumnsType, Space, Avatar, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// apis
import { McOrderListOptions } from '@/api';

// assets
import usdt from '@/assets/usdt.png';

// components
import { TagTransactionType } from '@/components/TagAlpha';
import TagStatusMcOrder from '@/components/TagAlpha/TagStatusMcOrder';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';

// hooks
import { dateFormator } from '@/hook';

// utils
import { CryptoEnum, cryptoEnumOptions, nTot, TxStatusMerchantOrder, valueToLabel } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';

type WalletColumnsProps = {};
const useWalletMerchantColumns = (useProps: WalletColumnsProps) => {
  // props
  const {} = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('useWalletMerchantColumns');

  // compute
  const renderAllMerchantStatus = () => {
    const keys = Object.keys(TxStatusMerchantOrder)
      .map((item) => {
        if (!Number.isNaN(Number(item))) {
          return Number(item);
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as Array<TxStatusMerchantOrder>;

    return keys.map((key) => {
      return {
        value: key,
        text: (
          <TagStatusMcOrder
            className='w-32 border text-center'
            status={key}
          />
        ),
        diabled: true,
      };
    });
  };
  const columns: TableColumnsType<McOrderListOptions> = [
    {
      title: <Txt>{t('type')}</Txt>,
      key: 'transactionType',
      align: 'center',
      render: ({ order }) => {
        return (
          <div className='pl-2'>
            <TagTransactionType transactionType={order.transactionType} />
          </div>
        );
      },
    },
    {
      key: 'merchantNumber',
      dataIndex: 'merchantNumber',
      title: <Txt>{t('merchant')}</Txt>,
      align: 'center',
      render: (_, { merchantName, merchantNumber }) => (
        <main className='flex flex-col pl-2 text-left'>
          <Space>
            <Txt type='secondary'>{t('merchantNumber')}:</Txt>
            <Txt>{merchantNumber}</Txt>
          </Space>
          <Space>
            <Txt type='secondary'>{t('merchantName')}:</Txt>
            <Txt>{merchantName}</Txt>
          </Space>
        </main>
      ),
    },
    {
      title: <Txt>{t('time')}</Txt>,
      key: 'time',
      align: 'center',
      render: ({ createdAt, order }) => {
        const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '--';
        const verifyFormat = order.confirmedAt ? dayjs(order.confirmedAt).format(dateFormator.accurate) : '--';

        return (
          <main className='flex justify-center'>
            <div className='flex flex-col text-left'>
              <Space>
                <Txt type='secondary'>{t('createdAt')}:</Txt>
                <Txt>{createFormat}</Txt>
              </Space>

              <Space>
                <Txt type='secondary'>{t('confirmedAt')}:</Txt>
                <Txt>{verifyFormat}</Txt>
              </Space>
            </div>
          </main>
        );
      },
    },
    {
      title: <Txt>{t('applicantName')}</Txt>,
      key: 'applicantName',
      align: 'center',
      render: ({ applicantName }) => {
        return <Txt>{applicantName || '--'}</Txt>;
      },
    },
    {
      title: <Txt>{t('approverName')}</Txt>,
      key: 'approverName',
      align: 'center',
      render: ({ approverName }) => {
        return <Txt>{approverName || '--'}</Txt>;
      },
    },
    {
      title: () => <Txt>{t('hash')}</Txt>,
      key: 'hash',
      align: 'center',
      width: 150,
      render: ({ order }) => {
        const isExpanded = expandedKeys.includes(order.hash || '');
        const isHovered = hoveredKeys.includes(order.hash || '');
        return (
          <TxtCompressible
            {...{ isWhite, isHovered, isExpanded }}
            text={order.hash || ''}
            setIsHover={setHoveredKeys}
            setIsExpanded={setExpandedKeys}
          />
        );
      },
    },
    {
      title: <Txt>{t('addresses')}</Txt>,
      key: 'TransAddress',
      align: 'center',
      render: ({ order }) => {
        return (
          <main className='flex justify-center'>
            <div className='flex flex-col text-left'>
              <Space>
                <Txt type='secondary'>{t('from')}:</Txt>
                {order.from ? (
                  <Tooltip title={order.from}>
                    <Txt
                      copyable
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      {order.from}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt
                    className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                    ellipsis
                  >
                    --
                  </Txt>
                )}
              </Space>

              <Space>
                <Txt type='secondary'>{t('to')}:</Txt>
                {order.to ? (
                  <Tooltip title={order.to}>
                    <Txt
                      copyable
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      {order.to}
                    </Txt>
                  </Tooltip>
                ) : (
                  <Txt
                    className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                    ellipsis
                  >
                    --
                  </Txt>
                )}
              </Space>
            </div>
          </main>
        );
      },
    },
    {
      title: <Txt>{t('crypto')}</Txt>,
      key: 'crypto',
      align: 'center',
      render: ({ order }) => {
        const avaSrc = (() => {
          if (order.cryptoType === CryptoEnum.TRC20_USDT) return usdt;
          if (order.cryptoType === CryptoEnum.ERC20_USDT) return usdt;
          return '';
        })();
        return (
          <Space>
            <Avatar
              size='small'
              src={avaSrc}
            />
            <Txt>{valueToLabel<CryptoEnum>(order.cryptoType, cryptoEnumOptions)}</Txt>
          </Space>
        );
      },
    },
    {
      title: () => <Txt>{t('gas')}</Txt>,
      key: 'gas',
      align: 'right',
      sorter: (a, b) => a.order.gas - b.order.gas,
      render: ({ order }) => {
        return <Txt>{order.gas ? nTot({ value: order.gas }) : '--'}</Txt>;
      },
    },
    {
      title: () => <Txt>{t('fee')}</Txt>,
      key: 'fee',
      align: 'right',
      sorter: (a, b) => a.order.fee - b.order.fee,
      render: ({ order }) => {
        return <Txt>{order.fee ? nTot({ value: order.fee }) : '--'}</Txt>;
      },
    },
    {
      title: () => <Txt>{t('amount')}</Txt>,
      key: 'amount',
      align: 'center',
      sorter: (a, b) => a.order.actualAmount - b.order.actualAmount,
      render: ({ order }) => {
        return (
          <main className='flex flex-col text-left'>
            <Space>
              <Txt type='secondary'>{t('requireAmount')}:</Txt>
              <Txt>{nTot({ value: order.requireAmount })}</Txt>
            </Space>
            <Space>
              <Txt type='secondary'>{t('actualAmount')}:</Txt>
              <Txt>{order.actualAmount ? nTot({ value: order.actualAmount }) : '--'}</Txt>
            </Space>
          </main>
        );
      },
    },
    {
      title: () => <Txt className='text-nowrap'>{t('status')}</Txt>,
      key: 'state',
      filters: renderAllMerchantStatus(),
      align: 'center',
      render: ({ order }) => {
        return (
          <TagStatusMcOrder
            status={
              order.status === TxStatusMerchantOrder.BlockchainConfirmed
                ? TxStatusMerchantOrder.Completed
                : order.status
            }
          />
        );
      },
    },
    {
      title: <Txt className='pr-2'>{t('remark')}</Txt>,
      key: 'remark',
      width: 200,
      render: ({ order }) => {
        return order.remark ? (
          <Txt
            copyable
            className='pr-2'
          >
            {order.remark}
          </Txt>
        ) : (
          <Txt>--</Txt>
        );
      },
    },
  ];

  return { columns };
};

export default useWalletMerchantColumns;
export type { WalletColumnsProps };
