import {
  Alert,
  Avatar,
  Button,
  Divider,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  Statistic,
  Typography,
} from 'antd';
import * as React from 'react';
import Decimal from 'decimal.js';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SubmitWithdrawalProps, useMcDetails, useSubmitWithdrawal } from '@/api';
import usdt from '@/assets/usdt.png';
import TransferVertify from '@/pages/wallet/components/TransferVertify';
import { nTot, tTon } from '@/utils';
import { useNotifyStore, useUserStore } from '@/store';

type TransferCreateValues = SubmitWithdrawalProps;
interface ITransferCreateProps {
  open: boolean;
  onCancel: () => void;
}

const TransferCreate: React.FunctionComponent<ITransferCreateProps> = ({ open, onCancel }) => {
  // states
  const [transferAmount, setTransferAmount] = useState<Decimal>(new Decimal(0));
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [openDepositVertify, setOpenDepositVertify] = useState(false);
  const [formValues, setFormValues] = useState<Omit<TransferCreateValues, 'operationPassword'> | null>(null);

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { info } = useUserStore();
  const { t } = useTranslation('transferCreate');

  const { data } = useMcDetails({});

  const availableBalance = React.useMemo(() => new Decimal(data?.wallet?.balance || 0), [data]);
  const withdrawalFee = React.useMemo(() => new Decimal(data?.fee?.[0]?.withdrawalFee || 0), [data]);
  const totalAmount = React.useMemo(
    () =>
      transferAmount.add(withdrawalFee).lt(0) || transferAmount.eq(0)
        ? new Decimal(0)
        : transferAmount.add(withdrawalFee),
    [withdrawalFee, transferAmount],
  );

  const deFaultAdressReceive = data?.receiveAddress?.[0]?.addresses;
  const handleAmountChange = (value: string) => {
    const amount = new Decimal(tTon(value) || 0);
    setTransferAmount(amount);
  };

  const { mutate: setCAOder, isPending: inLoad } = useSubmitWithdrawal({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
      form.setFieldsValue({ amount: new Decimal(0) });
    },
  });

  // handlers
  const handleSubmitForm = useCallback(
    (values: Omit<TransferCreateValues, 'operationPassword'>) => {
      const remark = values.remark || `${info?.nickName || 'Unknown'} withdraw`;
      setFormValues({ ...values, remark });
      setOpenDepositVertify(true);
    },
    [info?.nickName],
  );

  const handlePasswordSubmit = (password: string) => {
    if (formValues) {
      const payload = { ...formValues, operationPassword: password };
      setCAOder(payload);
      setOpenDepositVertify(false);
      form.resetFields();
    }
  };

  const options = [{ label: 'USDT-TRC20', value: 'USDT-TRC20', icon: usdt }];

  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={open}
        onCancel={onCancel}
        forceRender
      >
        <Form
          name='createForm'
          variant='filled'
          layout='vertical'
          initialValues={{
            to: deFaultAdressReceive || '',
            remark: `${info?.nickName || 'Unknown'} withdraw`,
            dvt: 'USDT-TRC20',
          }}
          onFinish={handleSubmitForm}
        >
          <>
            <Form.Item>
              <Alert
                className='mt-5'
                description={t('alertDescription')}
                type='warning'
                showIcon
              />
            </Form.Item>
            <Form.Item
              name='dvt'
              label={t('dvtLabel')}
            >
              <Select
                options={options}
                // eslint-disable-next-line react/no-unstable-nested-components
                optionRender={(option) => (
                  <Space>
                    <Avatar
                      size='small'
                      src={usdt}
                    />
                    <div>{option.label}</div>
                  </Space>
                )}
              />
            </Form.Item>
            <Form.Item>
              <Statistic
                title={t('availableBalance')}
                value={availableBalance.toString()}
                suffix={<div className='text-sm'>{t('usdtTrc20')}</div>}
                valueStyle={{ color: '#52c41a' }}
              />
            </Form.Item>
            <Form.Item>
              <Alert
                description={t('feeDescription')}
                type='info'
                showIcon
              />
            </Form.Item>
            <Form.Item
              label={t('amountLabel')}
              name='amount'
              rules={[
                {
                  validator: (_, value) => {
                    if (!value) {
                      return Promise.reject(new Error(t('amountErrorMessage')));
                    }

                    const cleanValue = value.toString().replace(/,/g, '');
                    const amount = new Decimal(cleanValue);

                    if (amount.gt(availableBalance)) {
                      return Promise.reject(
                        new Error(`${t('insufficient')} ${nTot({ value: availableBalance.toString() })}`),
                      );
                    }

                    return Promise.resolve();
                  },
                },
              ]}
              extra={
                transferAmount.add(withdrawalFee).lte(availableBalance)
                  ? `${t('remaining')}：${nTot({
                      value: availableBalance.minus(transferAmount).minus(withdrawalFee).toString(),
                    })}`
                  : null
              }
            >
              <InputNumber
                placeholder='$0'
                addonAfter='USDT-TRC20'
                value={transferAmount.isZero() ? '' : transferAmount.toString()}
                style={{ width: '100%' }}
                controls={false}
                formatter={(value) => {
                  if (!value) return '';
                  const [integerPart, decimalPart] = value.split('.');
                  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
                }}
                parser={(value) => {
                  if (!value) return '';
                  let cleanedValue = value.replace(/[^0-9.]/g, '');
                  const [integerPart, decimalPart] = cleanedValue.split('.');
                  if (decimalPart && decimalPart.length > 6) {
                    cleanedValue = `${integerPart}.${decimalPart.slice(0, 6)}`;
                  }
                  return cleanedValue;
                }}
                onPaste={(e) => {
                  e.preventDefault();
                  const pastedValue = e.clipboardData.getData('Text');
                  if (pastedValue) {
                    handleAmountChange(pastedValue);
                  } else {
                    setTransferAmount(new Decimal(0));
                  }
                }}
                onChange={(value) => {
                  if (value === null || value === '') {
                    setTransferAmount(new Decimal(0));
                    return;
                  }

                  const cleanValue = value.toString().replace(/[^0-9.]/g, '');
                  const isValid = /^[0-9]*(\.[0-9]{0,6})?$/.test(cleanValue);

                  if (isValid) {
                    handleAmountChange(cleanValue);
                  }
                }}
                onKeyDown={(e) => {
                  const { key, ctrlKey, metaKey } = e;
                  const allowedKeys = [
                    'Backspace',
                    'Delete',
                    'Tab',
                    'ArrowLeft',
                    'ArrowRight',
                    'ArrowUp',
                    'ArrowDown',
                    '.',
                  ];
                  const isNumber = /^\d$/.test(key);

                  if ((ctrlKey || metaKey) && key === 'a') return;

                  const currentValue = e.currentTarget.value;
                  if (key === '.' && currentValue.includes('.')) {
                    e.preventDefault();
                    return;
                  }

                  const decimalPart = currentValue.split('.')[1];
                  if (decimalPart && decimalPart.length >= 6 && /^\d$/.test(key)) {
                    e.preventDefault();
                    return;
                  }

                  if (!isNumber && !allowedKeys.includes(key)) {
                    e.preventDefault();
                  }
                }}
                disabled={inLoad}
              />
            </Form.Item>

            <Form.Item
              label={t('toLabel')}
              name='to'
              rules={[{ required: true, message: t('toErrorMessage') }]}
            >
              <Select
                placeholder={t('toPlaceholder')}
                onChange={setSelectedAddress}
                options={data?.receiveAddress?.map((addr) => ({
                  label: addr.addresses,
                  value: addr.addresses,
                }))}
                value={selectedAddress}
                disabled={inLoad}
              />
            </Form.Item>
            <Form.Item
              label={t('remarkLabel')}
              name='remark'
            >
              <Input.TextArea
                placeholder={t('remarkPlaceholder')}
                disabled={inLoad}
              />
            </Form.Item>
            <Form.Item>
              <Flex justify='space-between'>
                <Typography.Text type='secondary'>{t('transferAmount')}</Typography.Text>
                <Typography.Text type='secondary'>{nTot({ value: transferAmount.toString() })}</Typography.Text>
              </Flex>
              <Flex justify='space-between'>
                <Typography.Text type='secondary'>{t('fees')}</Typography.Text>
                <Typography.Text type='secondary'>{nTot({ value: withdrawalFee.toString() })}</Typography.Text>
              </Flex>
              <Divider className='m-1' />
              <Flex justify='space-between'>
                <Typography.Text
                  strong
                  className='text-[20px]'
                >
                  {t('estimatedArrival')}
                </Typography.Text>
                <Typography.Text
                  strong
                  className='text-[20px]'
                >
                  {nTot({ value: totalAmount.toString() })}
                </Typography.Text>
              </Flex>
            </Form.Item>
            <Form.Item>
              <Button
                block
                type='primary'
                htmlType='submit'
                loading={inLoad}
                disabled={
                  inLoad ||
                  transferAmount.lte(0) ||
                  transferAmount.add(withdrawalFee).gt(availableBalance) ||
                  transferAmount.lte(withdrawalFee)
                }
              >
                {t('submit')}
              </Button>
            </Form.Item>
          </>
        </Form>
      </Modal>
      <TransferVertify
        open={openDepositVertify}
        onCancel={() => setOpenDepositVertify(false)}
        onSubmit={handlePasswordSubmit}
        loading={inLoad}
        form={form}
      />
    </div>
  );
};

export default TransferCreate;
