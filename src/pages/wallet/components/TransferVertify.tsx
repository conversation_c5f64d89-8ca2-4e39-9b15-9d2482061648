// libs
import { LockOutlined } from '@ant-design/icons';
import { Button, Form, Input, Modal } from 'antd';
import * as React from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface ITransferVertifyProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (operationPassword: string) => void;
  loading: boolean;
  form?: any;
}

const TransferVertify: React.FunctionComponent<ITransferVertifyProps> = (props) => {
  // props
  const { open, onCancel, onSubmit, loading, form } = props || {};

  // states
  const [password, setPassword] = useState('');

  // hooks
  const { t } = useTranslation('transferVerify');

  // handlers
  const handlePasswordSubmit = () => {
    if (password.trim()) {
      onSubmit(password);
    }
  };

  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={open}
        onCancel={onCancel}
        centered
      >
        <Form
          name='verifyForm'
          form={form}
          variant='filled'
          layout='vertical'
          initialValues={{ remember: true }}
          onFinish={handlePasswordSubmit}
        >
          <Form.Item
            label={t('operationPasswordLabel')}
            name='operationPassword'
          >
            <Input.Password
              prefix={<LockOutlined />}
              type='operationPassword'
              placeholder={t('operationPasswordPlaceholder')}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          </Form.Item>
          <Form.Item>
            <Button
              block
              type='primary'
              htmlType='submit'
              loading={loading}
            >
              {t('submit')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TransferVertify;
