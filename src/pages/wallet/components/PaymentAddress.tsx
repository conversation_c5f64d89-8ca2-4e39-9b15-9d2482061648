// libs
import React from 'react';
import { Space, QRCode, Avatar, Divider, Typography, Button, Skeleton } from 'antd';
import { useTranslation } from 'react-i18next';

// assets
import trx from '@/assets/trx.png';
import eth from '@/assets/eth.png';

// utils
import { CryptoEnum, cryptoEnumOptions, valueToLabel } from '@/utils';

interface PaymentAddressProps {
  walletType: CryptoEnum;
  walletAddress: string;
  supportedCurrencies: string[]; // support description
  isWhite: boolean;
  onTransferClick: () => void;
  loading: boolean;
}

const PaymentAddress: React.FC<PaymentAddressProps> = ({
  walletType,
  walletAddress,
  supportedCurrencies,
  isWhite,
  onTransferClick,
  loading,
}) => {
  // eslint-disable-next-line no-nested-ternary
  const avatarSrc = walletType === CryptoEnum.TRC20_USDT ? trx : walletType === CryptoEnum.ERC20_USDT ? eth : '';

  // hooks
  const { t } = useTranslation('paymentAddress');

  return (
    <Space size='large'>
      <>
        {loading ? (
          <Skeleton.Node
            active
            style={{ width: 140, height: 140, marginBottom: 10 }}
          />
        ) : (
          <QRCode
            value={walletAddress || '-'}
            size={140}
          />
        )}
        <Space direction='vertical'>
          <Space>
            <Avatar src={avatarSrc} />
            <div className='text-3xl font-bold'>{valueToLabel(walletType, cryptoEnumOptions) || '-'}</div>
          </Space>
          <Divider type='vertical' />
          <div>
            {t('receiveWalletDescription')}
            <span className='font-bold text-[#52c41a]'>{supportedCurrencies.join('、')}</span>
            {t('cryptocurrency')}
          </div>
          <Space>
            {loading ? (
              <Skeleton.Input
                active
                size='small'
              />
            ) : (
              <Typography.Text
                className='rounded-md p-2'
                style={{ background: isWhite ? '#f1f1f1' : '#141414' }}
                copyable
              >
                {walletAddress}
              </Typography.Text>
            )}

            <Button
              type='primary'
              onClick={onTransferClick}
              loading={loading}
            >
              {t('transferButton')}
            </Button>
          </Space>
        </Space>
      </>
    </Space>
  );
};

export default PaymentAddress;
