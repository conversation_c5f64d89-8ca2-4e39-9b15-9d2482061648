// libs
import { useCallback, useRef } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { OTPRef } from 'antd/es/input/OTP';
import { useTranslation } from 'react-i18next';

// api
import { useVfy2Fa } from '@/api';

type SubmitVfyValues = { TwoFactorCode: string };

interface ILoginVertifyProps {
  open: boolean;
  onCancel: () => void;
}

const LoginVertify: React.FunctionComponent<ILoginVertifyProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // refs
  const otpRef = useRef<OTPRef>(null);

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('loginVerify');

  const { mutate: vfy, isPending: inVerify } = useVfy2Fa({
    onError: () => {
      form.resetFields();
    },
  });

  const handleSubmit = useCallback(
    (values: SubmitVfyValues) => {
      vfy(values);
    },
    [vfy],
  );

  const handleOtpChange = (value: string) => {
    if (value.length === 6) {
      form.submit();
    }
  };

  const handleModalClose = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={t('title')}
      footer={false}
      centered
      open={open}
      onCancel={handleModalClose}
      afterOpenChange={(visible) => {
        if (visible) otpRef.current?.focus();
      }}
    >
      <Form
        name={t('formName')}
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
      >
        <Form.Item
          label={t('twoFactorLabel')}
          name='TwoFactorCode'
        >
          <Input.OTP
            ref={otpRef}
            autoFocus
            formatter={(value) => value.replace(/\D/g, '')}
            onChange={handleOtpChange}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inVerify}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default LoginVertify;
