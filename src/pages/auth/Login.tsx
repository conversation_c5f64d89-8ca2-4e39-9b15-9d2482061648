// libs
import * as React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Button, Card, Col, Form, Input, Row, Typography, Radio } from 'antd';
import { UserOutlined, LockOutlined, ShopOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// apis
import { useLogin } from '@/api';

// utils
import validatePassword from '@/utils';

// pages
import LoginVertify from './components/LoginVertify';

const initialValues = import.meta.env.DEV
  ? {
      merchantNumber: '550548',
      username: 'merchantadmin1',
      password: 'So123456789',
    }
  : undefined;

type LoginSubmitValues = {
  merchantNumber: string;
  username: string;
  password: string;
};

interface ILoginProps {}
const Login: React.FunctionComponent<ILoginProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [loginUserType, setLoginUserType] = useState<string>('merchantadmin1');
  const [openLoginVertify, setOpenLoginVertify] = useState(false);

  // hooks
  const [form] = Form.useForm();
  const { t } = useTranslation('login');

  // mutate
  const { mutate: login, isPending: inLogin } = useLogin({
    onSuccess: (res) => {
      if (res.requiredTwoFactor) setOpenLoginVertify(true);
    },
  });

  // compute

  const handleSubmit = useCallback(
    (values: LoginSubmitValues) => {
      const { merchantNumber, username, password } = values;
      login({
        merchantNumber,
        username,
        password,
      });
    },
    [login],
  );

  useEffect(() => {
    const isTestLogin = true;
    if (!import.meta.env.DEV || !isTestLogin) return;
    if (['So1CSMerchant', 'So2CSMerchant', 'So3CSMerchant', 'MerchantAdmin3'].includes(loginUserType)) {
      form.setFieldValue('merchantNumber', '354476');
    } else {
      form.setFieldValue('merchantNumber', '550548');
    }
    form.setFieldValue('username', loginUserType);
    form.setFieldValue('password', 'So123456789');
  }, [form, loginUserType]);

  return (
    <div>
      <Row
        wrap
        gutter={[30, 10]}
      >
        <Col flex='auto'>
          <Typography.Title level={3}>{t('title')}</Typography.Title>
          <Typography.Text type='secondary'>{t('slogan')}</Typography.Text>
        </Col>
        <Col flex='auto'>
          <Card title={t('cardTitle')}>
            <Form
              {...{ form, initialValues }}
              variant='filled'
              layout='vertical'
              onFinish={handleSubmit}
            >
              <Form.Item
                label={t('merchantNumberLabel')}
                rules={[{ required: true, message: t('merchantNumberErrorMessage') }]}
                name='merchantNumber'
              >
                <Input
                  prefix={<ShopOutlined />}
                  placeholder={t('merchantNumberPlaceholder')}
                />
              </Form.Item>
              <Form.Item
                label={t('usernameLabel')}
                rules={[{ required: true, message: t('usernameErrorMessage') }]}
                name='username'
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder={t('usernamePlaceholder')}
                />
              </Form.Item>
              <Form.Item
                label={t('passwordLabel')}
                rules={[{ validator: validatePassword }]}
                name='password'
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('passwordPlaceholder')}
                />
              </Form.Item>
              <Form.Item>
                <Button
                  block
                  type='primary'
                  htmlType='submit'
                  loading={inLogin}
                >
                  {t('submit')}
                </Button>
              </Form.Item>
            </Form>
            {import.meta.env.DEV && (
              <div className='space-x-2'>
                <Typography.Text type='secondary'>行政:</Typography.Text>
                <Radio.Group
                  className='space-x-2'
                  value={loginUserType}
                  onChange={(e) => setLoginUserType(e.target.value)}
                >
                  <Radio.Button value='MerchantAdmin3'>MerchantAdmin3</Radio.Button>
                </Radio.Group>
              </div>
            )}

            {import.meta.env.DEV && (
              <div className='mt-2 space-x-2'>
                <Typography.Text
                  type='secondary'
                  className='mr-3'
                >
                  CS:
                </Typography.Text>
                <Radio.Group
                  className='space-x-2'
                  value={loginUserType}
                  onChange={(e) => setLoginUserType(e.target.value)}
                >
                  <Radio.Button value='So1CSMerchant'>Cs1</Radio.Button>
                  <Radio.Button value='So2CSMerchant'>Cs2</Radio.Button>
                  <Radio.Button value='So3CSMerchant'>Cs3</Radio.Button>
                  <Radio.Button value='So13CSMerchant'>Cs13</Radio.Button>
                </Radio.Group>
              </div>
            )}
          </Card>
        </Col>
      </Row>
      <LoginVertify
        open={openLoginVertify}
        onCancel={() => setOpenLoginVertify(false)}
      />
    </div>
  );
};

export default Login;
