// libs
import { useCallback, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { Avatar, Space, TableColumnsType, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

// apis
import { OderListMerchantOptions, OderListMerchantOptionsRes } from '@/api';

// assets
import usdt from '@/assets/usdt.png';

// components
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagTxStatus, TagTxType } from '@/components/TagAlpha';

// hooks
import { dateFormator } from '@/hook';

// utils
import { CryptoEnum, cryptoEnumOptions, nTot, valuesToFilter, valueToLabel } from '@/utils';

// store
import { useThemeStore } from '@/store/useThemeStore';

type DepositColumnsProps = {
  data: OderListMerchantOptionsRes | undefined;
  setDepositMatchFrom: React.Dispatch<React.SetStateAction<OderListMerchantOptions | undefined>>;
};
const useDepositMerchantColumns = (useProps: DepositColumnsProps) => {
  // props
  const { data, setDepositMatchFrom } = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('useDepositMerchantColumns');

  // compute
  const onClickStatus = useCallback(
    (row: OderListMerchantOptions) => {
      setDepositMatchFrom(data?.items.find((findI) => findI.orderUid === row.orderUid));
    },
    [setDepositMatchFrom, data?.items],
  );

  const columns = useMemo(() => {
    if (!Array.isArray(data?.items)) return [];
    const cryptoFilters = valuesToFilter<CryptoEnum>(
      Array.from(new Map(data?.items.map((mapD) => [mapD.order.cryptoType, mapD.order.cryptoType])).values()),
      cryptoEnumOptions,
    );

    const result: TableColumnsType<OderListMerchantOptions> = [
      {
        key: 'merchantNumber',
        dataIndex: 'merchantNumber',
        title: <Txt>{t('merchant')}</Txt>,
        align: 'center',
        render: (_, { merchantName, merchantNumber }) => (
          <main className='flex flex-col pl-2 text-left'>
            <Space>
              <Txt type='secondary'>{t('merchantNumber')}:</Txt>
              <Txt>{merchantNumber}</Txt>
            </Space>
            <Space>
              <Txt type='secondary'>{t('merchantName')}:</Txt>
              <Txt>{merchantName}</Txt>
            </Space>
          </main>
        ),
      },
      {
        title: <Txt>{t('type')}</Txt>,
        key: 'transactionType',
        align: 'center',
        render: ({ order }) => {
          return <TagTxType transactionType={order.transactionType} />;
        },
      },
      {
        title: <Txt>{t('time')}</Txt>,
        key: 'time',
        align: 'center',
        render: ({ createdAt, order }) => {
          const createFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '--';
          const verifyFormat = order.confirmedAt ? dayjs(order.confirmedAt).format(dateFormator.accurate) : '--';

          return (
            <main className='flex flex-col text-left'>
              <Space>
                <Txt type='secondary'>{t('createdAt')}:</Txt>
                <Txt type={createdAt ? undefined : 'secondary'}>{createFormat}</Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('confirmedAt')}:</Txt>
                <Txt type={order.confirmedAt ? undefined : 'secondary'}>{verifyFormat}</Txt>
              </Space>
            </main>
          );
        },
      },
      {
        title: () => <Txt>{t('orderUid')}</Txt>,
        key: 'orderUid',
        align: 'center',
        render: ({ orderUid }) => {
          const isExpanded = expandedKeys.includes(orderUid || '');
          const isHovered = hoveredKeys.includes(orderUid || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              text={orderUid || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: () => <Txt>{t('merchantOrderNumber')}</Txt>,
        key: 'merchantOrderNumber',
        align: 'center',
        render: ({ merchantOrderId }) => {
          const isExpanded = expandedKeys.includes(merchantOrderId || '');
          const isHovered = hoveredKeys.includes(merchantOrderId || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              text={merchantOrderId || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: () => <Txt>{t('hash')}</Txt>,
        key: 'hash',
        align: 'center',
        width: 150,
        render: ({ order }) => {
          const isExpanded = expandedKeys.includes(order.hash || '');
          const isHovered = hoveredKeys.includes(order.hash || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              text={order.hash || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('transactionAddress')}</Txt>,
        key: 'TransAddress',
        align: 'center',
        render: ({ order }) => {
          return (
            <main className='flex justify-center'>
              <div className='flex flex-col text-left'>
                <Space>
                  <Txt type='secondary'>{t('from')}:</Txt>
                  {order.from ? (
                    <Tooltip title={order.from}>
                      <Txt
                        copyable
                        className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                        ellipsis
                      >
                        {order.from}
                      </Txt>
                    </Tooltip>
                  ) : (
                    <Txt
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      --
                    </Txt>
                  )}
                </Space>

                <Space>
                  <Txt type='secondary'>{t('to')}:</Txt>
                  {order.to ? (
                    <Tooltip title={order.to}>
                      <Txt
                        copyable
                        className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                        ellipsis
                      >
                        {order.to}
                      </Txt>
                    </Tooltip>
                  ) : (
                    <Txt
                      className='w-32 overflow-hidden text-ellipsis whitespace-nowrap'
                      ellipsis
                    >
                      --
                    </Txt>
                  )}
                </Space>
              </div>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('crypto')}</Txt>,
        key: 'crypto',
        align: 'center',
        render: (_, { order }) => {
          const src = (() => {
            if (order.cryptoType === CryptoEnum.TRC20_USDT) return usdt;
            if (order.cryptoType === CryptoEnum.ERC20_USDT) return usdt;
            return '';
          })();

          return (
            <Space>
              <Avatar
                {...{ src }}
                size='small'
              />
              <div className='text-nowrap'>{valueToLabel(order.cryptoType, cryptoEnumOptions)}</div>
            </Space>
          );
        },
        filters: cryptoFilters,
      },
      {
        title: () => <Txt>{t('fee')}</Txt>,
        key: 'fee',
        align: 'right',
        sorter: (a, b) => a.order.fee - b.order.fee,
        render: ({ order }) => {
          return <Txt>{order.fee ? nTot({ value: order.fee }) : '--'}</Txt>;
        },
      },
      {
        title: () => <Txt>{t('amount')}</Txt>,
        key: 'amount',
        align: 'center',
        sorter: (a, b) => a.order.actualAmount - b.order.actualAmount,
        render: ({ order }) => {
          return (
            <main className='flex flex-col text-left'>
              <Space>
                <Txt type='secondary'>{t('requireAmount')}:</Txt>
                <Txt>{nTot({ value: order.requireAmount })}</Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('actualAmount')}:</Txt>
                <Txt>{order.actualAmount ? nTot({ value: order.actualAmount }) : '--'}</Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('transferAmount')}:</Txt>
                <Txt>{order.actualAmount ? nTot({ value: order.requireAmount - order.fee }) : '--'}</Txt>
              </Space>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('status')}</Txt>,
        key: 'status',
        align: 'center',
        render: (_, item) => {
          const { order } = item;
          return (
            <TagTxStatus
              status={order.status}
              tooltip={t('statusTooltip')}
              onClick={() => onClickStatus(item)}
            />
          );
        },
      },
    ];

    return result;
  }, [data?.items, expandedKeys, hoveredKeys, isWhite, onClickStatus, t]);
  return { columns };
};

export default useDepositMerchantColumns;
