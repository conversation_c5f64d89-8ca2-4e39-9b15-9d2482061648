import * as React from 'react';
import { useCallback, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>rumb, Flex, Select } from 'antd';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { OderListMerchantOptions, useMemberOderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import BtnFuncs from '@/components/BtnFuncs';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import {
  cryptoEnumOptions,
  exportSheetByArray,
  storageHelper,
  TxCategoryNum,
  txCategoryNumOptions,
  TxStatusNum,
  txStatusOptions,
} from '@/utils';
import { Txt } from '@/components/TypographyMaster';
import OrderDetails from '../OrderDetails';
import DepositCreate from './components/DepositCreate';
import useDepositMerchantColumns from './useDepositMerchantColumns';

interface IDepositProps {
  isActive?: boolean;
}

const Deposit: React.FunctionComponent<IDepositProps> = (props) => {
  // props
  const {} = props || {};

  const storageRange = storageHelper<{ from: string; to: string }>('depositRange').getItem();
  const { t } = useTranslation('deposit');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'deposit' });
  const [openDepositCreate, setOpenDepositCreate] = useState(false);
  const [depositMatchFrom, setDepositMatchFrom] = useState<OderListMerchantOptions>();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const [OrderUid, setOrderUid] = useState<string>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [Hash, setHash] = useState<string>();
  const [Status, setStatus] = useState<Array<TxStatusNum>>();
  const { data, isPending, isRefetching } = useMemberOderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      TransactionType: TxCategoryNum.Deposit,
      IsUncompleted: true,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      OrderUid,
      MerchantOrderId,
      Hash,
      Status,
    },
  });

  // compute
  const translateTxStatusOptions = useMemo(() => {
    return txStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) }));
  }, [optionsT]);

  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);
  const { columns } = useDepositMerchantColumns({ data, setDepositMatchFrom });
  // const summary = useDepositSummary({ columns });
  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('depositRange').setItem(newDate);
  }, []);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex className='my-5 flex flex-wrap justify-between gap-2'>
        <Flex gap={10}>
          {/* 勾選後可點擊 */}
          {/* <Tooltip title='批量驗證，請勾選欲驗證的訂單'>
            <Button
              icon={<CheckOutlined />}
              shape='round'
              type='primary'
              onClick={() => setOpenDepositVertify(true)}
              disabled
            >
              已驗證
            </Button>
          </Tooltip> */}
          <BtnFuncs
            shape='round'
            iconType='add'
            type='primary'
            onClick={() => setOpenDepositCreate(true)}
            loading={isPending}
          >
            {t('createButton')}
          </BtnFuncs>
        </Flex>
        <Flex
          gap={10}
          className='items-center'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />

          <BtnFuncs
            iconType='print'
            loading={isRefetching}
            onClick={() => {
              const sheetTitleRow = [
                'merchantNumberTitleRow',
                'merchantNameTitleRow',
                'typeTitleRow',
                'createdAtTitleRow',
                'confirmedAtTitleRow',
                'orderUidTitleRow',
                'merchantOrderNumberTitleRow',
                'transactionHashTitleRow',
                'fromAddressTitleRow',
                'toAddressTitleRow',
                'cryptoTitleRow',
                'feeTitleRow',
                'requiredTitleRow',
                'actualTitleRow',
                'transferTitleRow',
                'statusTitleRow',
              ];
              const sheetDataRows = [
                ...dataSource.map((deposit) => {
                  const transactionType = txCategoryNumOptions.find(
                    (findO) => findO.value === deposit.order.transactionType,
                  );
                  const cryptoType = cryptoEnumOptions.find((findO) => findO.value === deposit.order.cryptoType);
                  const statusOption = txStatusOptions.find((findO) => findO.value === deposit.order.status);
                  const createdAt = deposit.createdAt ? dayjs(deposit.createdAt).format(dateFormator.accurate) : '';
                  const confirmedAt = deposit.order.confirmedAt
                    ? dayjs(deposit.order.confirmedAt).format(dateFormator.accurate)
                    : '';
                  return [
                    deposit.merchantNumber,
                    deposit.merchantName,
                    optionsT(transactionType?.label || 'undefined'),
                    createdAt,
                    confirmedAt,
                    deposit.orderUid,
                    deposit.merchantOrderId,
                    deposit.order.hash,
                    deposit.order.from,
                    deposit.order.to,
                    optionsT(cryptoType?.label || 'undefined'),
                    deposit.order.fee,
                    deposit.order.requireAmount,
                    deposit.order.actualAmount,
                    deposit.order.requireAmount - deposit.order.fee,
                    optionsT(statusOption?.label || 'undefined'),
                  ];
                }),
              ];

              exportSheetByArray({
                arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
                sheetName: t('filename'),
                fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
                  dateFormator.accurate,
                )} `,
              });
            }}
          />
        </Flex>
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='orderUid'
        titleRender={
          <div className='items- flex flex-wrap items-center gap-x-2'>
            <div className='flex flex-col gap-y-1'>
              <Txt
                type='secondary'
                className='font-bold'
              >
                {t('statusLabel')}:
              </Txt>
              <Select
                placeholder={t('statusPlaceholder')}
                variant='filled'
                className='w-[160px]'
                options={translateTxStatusOptions}
                disabled={isRefetching}
                onChange={(newValue) => setStatus(newValue)}
                allowClear
              />
            </div>
            <SearchMaster
              titles={[
                { key: 'OrderUid', label: t('orderUidLabel') },
                { key: 'MerchantOrderId', label: t('merchantOrderIdLabel') },
                { key: 'Hash', label: t('hashLabel') },
              ]}
              onSearch={(values) => {
                setOrderUid(values.OrderUid || undefined);
                setMerchantOrderId(values.MerchantOrderId || undefined);
                setHash(values.Hash || undefined);
              }}
            />
          </div>
        }
      />
      <OrderDetails
        order={depositMatchFrom}
        setOrder={setDepositMatchFrom}
      />
      <DepositCreate
        open={openDepositCreate}
        onCancel={() => setOpenDepositCreate(false)}
      />
      {/* <DepositVertify
        open={openDepositVertify}
        onCancel={() => setOpenDepositVertify(false)}
      /> */}
    </div>
  );
};

export default Deposit;
