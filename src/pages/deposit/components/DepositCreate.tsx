import * as React from 'react';
import { useCallback, useState } from 'react';
import { Alert, Button, Form, Input, Modal, Space, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useCreateAnomalyOrder, CreateAnomalyOrderProps } from '@/api';
import { useNotifyStore, useUserStore } from '@/store';
import DepositVertify from '@/pages/deposit/components/DepositVertify';

type ChOpwSubmitValues = CreateAnomalyOrderProps;
interface IDepositCreateProps {
  open: boolean;
  onCancel: () => void;
}

const DepositCreate: React.FunctionComponent<IDepositCreateProps> = ({ open, onCancel }) => {
  // states
  const [openDepositVertify, setOpenDepositVertify] = useState(false);
  const [formValues, setFormValues] = useState<Omit<ChOpwSubmitValues, 'operationPassword'> | null>(null);

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { info } = useUserStore();
  const { t } = useTranslation('depositCreate');

  const { mutate: setCAOder, isPending: inLoad } = useCreateAnomalyOrder({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
    },
    onError: () => {
      form.resetFields();
    },
  });

  const handleSubmit = useCallback(
    (values: Omit<ChOpwSubmitValues, 'operationPassword'>) => {
      const remark = values.remark || `${info?.nickName || 'Unknown'} withdraw`;
      setFormValues({ ...values, remark });
      setOpenDepositVertify(true);
    },
    [info?.nickName],
  );

  const handlePasswordSubmit = (password: string) => {
    if (formValues) {
      const payload = { ...formValues, operationPassword: password };
      setCAOder(payload);
      setOpenDepositVertify(false);
      form.resetFields();
    }
  };

  const handleModalClose = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={open}
        onCancel={handleModalClose}
      >
        <Form
          variant='filled'
          layout='vertical'
          onFinish={handleSubmit}
        >
          <Form.Item>
            <Alert
              message={t('alertMessage')}
              type='warning'
            />
          </Form.Item>
          <Form.Item
            label={
              <Space>
                <div>{t('hashLabel')}</div>
                <Tooltip title={t('hashTooltip')}>
                  <QuestionCircleOutlined className='text-[#52c41a]' />
                </Tooltip>
              </Space>
            }
            name='hash'
            rules={[{ required: true, message: t('hashErrorMessage') }]}
          >
            <Input
              placeholder={t('hashPlaceholder')}
              disabled={inLoad}
            />
          </Form.Item>
          <Form.Item
            label={t('remarkLabel')}
            name='remark'
          >
            <Input.TextArea
              placeholder={t('remarkPlaceholder')}
              value={info?.nickName || 'member deposit'}
              defaultValue={`${info?.nickName || 'Unknown'} deposit`}
              disabled={inLoad}
            />
          </Form.Item>
          <Form.Item>
            <Button
              block
              type='primary'
              htmlType='submit'
              loading={inLoad}
            >
              {t('submit')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      <DepositVertify
        open={openDepositVertify}
        onCancel={() => setOpenDepositVertify(false)}
        onSubmit={handlePasswordSubmit}
        loading={inLoad}
      />
    </div>
  );
};

export default DepositCreate;
