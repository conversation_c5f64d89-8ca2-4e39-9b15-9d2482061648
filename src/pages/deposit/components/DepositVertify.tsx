// libs
import * as React from 'react';
import { useState } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface IDepositVertifyProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (operationPassword: string) => void;
  loading: boolean;
}

const DepositVertify: React.FunctionComponent<IDepositVertifyProps> = ({ open, onCancel, onSubmit, loading }) => {
  // states
  const [password, setPassword] = useState('');

  // hooks
  const { t } = useTranslation('depositVerify');

  // handlers
  const handlePasswordSubmit = () => {
    if (password.trim()) {
      onSubmit(password);
    }
  };

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={open}
      onCancel={onCancel}
      centered
    >
      <Form
        name={t('formName')}
        variant='filled'
        layout='vertical'
        initialValues={{ remember: true }}
        onFinish={handlePasswordSubmit}
      >
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
        >
          <Input.Password
            prefix={<LockOutlined />}
            type={t('operationPasswordType')}
            placeholder={t('operationPasswordPlaceholder')}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={loading}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DepositVertify;
