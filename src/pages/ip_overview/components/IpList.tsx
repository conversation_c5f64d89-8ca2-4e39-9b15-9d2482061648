import * as React from 'react';
import { useMemo, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Flex, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { IpListInterface, useIpList } from '@/api';
import TableAlpha from '@/components/TableAlpha';
import SearchMaster from '@/components/SearchMaster';
import BtnFuncs from '@/components/BtnFuncs';
import { useTableStates } from '@/hook';
import useIpListColumns from './useIpListColumns';
import IpCreate from './IpCreate';
import IpDelete from './IpDelete';
import IpUpdate from './IpUpdate';

interface IpListProps {}

const IpList: React.FunctionComponent<IpListProps> = (props) => {
  // props
  const {} = props || {};

  // states
  const [openUpdateIp, setOpenUpdateIp] = useState<IpListInterface>();
  const [openDeleteIp, setOpenDeleteIp] = useState<IpListInterface>();
  const [ipAddress, setIpAddress] = useState<string>();
  const [openIpCreate, setOpenIpCreate] = useState(false);

  // hooks
  const { t } = useTranslation('ipList');
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const { data, isPending, isRefetching } = useIpList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      ipAddress,
    },
  });

  const { columns } = useIpListColumns({ data, setOpenUpdateIp, setOpenDeleteIp });
  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  return (
    <div>
      <Flex
        justify='space-between'
        className='my-5'
      >
        <Space>
          <BtnFuncs
            icon={<PlusOutlined />}
            loading={isRefetching}
            iconType='add'
            shape='round'
            type='primary'
            onClick={() => setOpenIpCreate(true)}
          >
            {t('addButton')}
          </BtnFuncs>
        </Space>
        {/* <BtnFuncs */}
        {/*   iconType='print' */}
        {/*   loading={isRefetching} */}
        {/*   onClick={() => { */}
        {/*     const currentTime = dayjs().format('YYYY-MM-DD'); */}
        {/*     exportSheetByArray({ */}
        {/*       arrays: arrayObjToArraySheet(dataSource.map((mapData) => ({ ...mapData }))), */}
        {/*       sheetName: `${currentTime}`, */}
        {/*       fileName: `${t('fileName')} ${dayjs().format(dateFormator.clock)}`, */}
        {/*     }); */}
        {/*   }} */}
        {/* /> */}
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending || isRefetching}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='ipAddress'
        titleRender={
          <SearchMaster
            titles={[{ key: 'ipAddress', label: t('ipAddressLabel') }]}
            onSearch={(values) => {
              setIpAddress(values.ipAddress || undefined);
            }}
          />
        }
      />
      <IpCreate
        open={openIpCreate}
        onCancel={() => setOpenIpCreate(false)}
      />
      <IpDelete
        open={!!openDeleteIp}
        ipAddress={openDeleteIp?.ipAddress}
        onCancel={() => setOpenDeleteIp(undefined)}
      />
      <IpUpdate
        open={!!openUpdateIp}
        ipAddress={openUpdateIp?.ipAddress}
        currentDescription={openUpdateIp?.description}
        onCancel={() => setOpenUpdateIp(undefined)}
      />
    </div>
  );
};

export default IpList;
