// libs
import * as React from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { useCreateIp } from '@/api';

// store
import { useNotifyStore } from '@/store';

interface IpCreateProps {
  open: boolean;
  onCancel: () => void;
}

const IpCreate: React.FunctionComponent<IpCreateProps> = ({ open, onCancel }) => {
  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('ipCreate');

  const { mutate: setCreateIP, isPending: inLoad } = useCreateIp({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
      form.resetFields();
    },
  });

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        form={form}
        name={t('formName')}
        variant='filled'
        layout='vertical'
        onFinish={(value) => {
          setCreateIP(value);
        }}
        className='mt-4'
      >
        <Form.Item
          label={t('ipAddressLabel')}
          name='ipAddress'
          rules={[{ required: true, message: t('ipAddressErrorMessage') }]}
        >
          <Input
            placeholder={t('ipAddressPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item
          label={t('descriptionLabel')}
          name='description'
          rules={[{ required: true, message: t('descriptionErrorMessage') }]}
        >
          <Input
            placeholder={t('descriptionPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t('operationPasswordPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLoad}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default IpCreate;
