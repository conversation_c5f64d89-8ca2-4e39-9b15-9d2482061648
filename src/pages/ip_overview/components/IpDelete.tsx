// libs
import * as React from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { useDeleteIp } from '@/api';

// utils
import { logWarn } from '@/utils';

// store
import { useNotifyStore } from '@/store';

interface IpDeleteProps {
  open: boolean;
  ipAddress?: string; // IP need delete
  onCancel: () => void;
}

const IpDelete: React.FunctionComponent<IpDeleteProps> = ({ open, ipAddress, onCancel }) => {
  // libs
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('ipDelete');

  const { mutate: deleteIp, isPending: inLoad } = useDeleteIp({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
      form.resetFields();
    },
  });

  const handleFinish = (values: { operationPassword: string }) => {
    if (ipAddress) {
      deleteIp({ ipAddress, operationPassword: values.operationPassword });
    } else {
      logWarn('IP Address is undefined');
    }
  };

  return (
    <Modal
      title={`${t('title')} ${ipAddress}`}
      footer={null}
      open={open}
      onCancel={onCancel}
    >
      <Form
        form={form}
        name='deleteIpForm'
        layout='vertical'
        onFinish={handleFinish}
        className='mt-4'
      >
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t('operationPasswordPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLoad}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default IpDelete;
