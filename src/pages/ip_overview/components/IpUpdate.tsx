// libs
import * as React from 'react';
import { useEffect } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// api
import { useUpdateIp } from '@/api';

// utils
import { logWarn } from '@/utils';

// store
import { useNotifyStore } from '@/store';

interface IpUpdateProps {
  open: boolean;
  ipAddress?: string;
  currentDescription?: string;
  onCancel: () => void;
}

const IpUpdate: React.FunctionComponent<IpUpdateProps> = ({ open, ipAddress, currentDescription, onCancel }) => {
  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('ipUpdate');

  const { mutate: updateIp, isPending: inLoad } = useUpdateIp({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
      form.resetFields();
    },
  });

  useEffect(() => {
    if (open) {
      form.setFieldsValue({ description: currentDescription });
    }
  }, [open, currentDescription, form]);

  const handleFinish = (values: { description: string; operationPassword: string }) => {
    if (ipAddress) {
      updateIp({
        ipAddress,
        newDescription: values.description,
        operationPassword: values.operationPassword,
      });
    } else {
      logWarn('IP Address is undefined');
    }
  };

  return (
    <Modal
      title={`${t('title')} ${ipAddress}`}
      footer={null}
      open={open}
      onCancel={onCancel}
    >
      <Form
        form={form}
        name='updateIpForm'
        layout='vertical'
        onFinish={handleFinish}
        className='mt-4'
      >
        <Form.Item
          label={t('newDescriptionLabel')}
          name='description'
          rules={[{ required: true, message: t('newDescriptionErrorMessage') }]}
        >
          <Input
            placeholder={t('newDescriptionPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item
          label={t('operationPasswordLabel')}
          name='operationPassword'
          rules={[{ required: true, message: t('operationPasswordErrorMessage') }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={t('operationPasswordPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLoad}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default IpUpdate;
