// libs
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { TableColumnsType } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// apis
import { IpListInterface, IpListRes } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// hooks
import { dateFormator } from '@/hook';

type IpList = {
  data: IpListRes | undefined;
  setOpenUpdateIp: ReactSet<IpListInterface | undefined>;
  setOpenDeleteIp: ReactSet<IpListInterface | undefined>;
};

const useIpListColumns = (useProps: IpList) => {
  // props
  const { setOpenDeleteIp, setOpenUpdateIp } = useProps;

  // hooks
  const { t } = useTranslation('useIpListColumns');

  // compute
  const columns = useMemo(() => {
    const result: TableColumnsType<IpListInterface> = [
      {
        title: () => <Txt>{t('createdAt')}</Txt>,
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt: string) => <Txt>{dayjs(createdAt).format(dateFormator.accurate)}</Txt>,
      },
      {
        title: () => <Txt>{t('ipAddress')}</Txt>,
        key: 'ipAddress',
        align: 'center',
        dataIndex: 'ipAddress',
        render: (ipAddress: string) => <Txt>{ipAddress}</Txt>,
      },
      {
        title: () => <Txt>{t('description')}</Txt>,
        key: 'description',
        align: 'center',
        dataIndex: 'description',
      },
      {
        title: () => <Txt>{t('actions')}</Txt>,
        key: 'operate',
        align: 'center',
        render: (_, record) => (
          <div className='flex justify-around'>
            <DeleteOutlined
              className='hover:opacity-65 cursor-pointer'
              onClick={() => setOpenDeleteIp(record)}
            />
            <EditOutlined
              className='hover:opacity-65 cursor-pointer'
              onClick={() => setOpenUpdateIp(record)}
            />
          </div>
        ),
      },
    ];

    return result;
  }, [setOpenDeleteIp, setOpenUpdateIp, t]);

  return { columns };
};

export default useIpListColumns;
