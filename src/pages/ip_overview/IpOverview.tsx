// libs
import { <PERSON> } from 'react-router-dom';
import { Breadcrumb } from 'antd';
import { useTranslation } from 'react-i18next';

// components
import IpList from './components/IpList';

interface IIpOverviewProps {}
const IpOverview: React.FunctionComponent<IIpOverviewProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { t } = useTranslation('ipOverview');

  return (
    <>
      <div style={{ padding: '24px' }}>
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            {
              title: <Link to='/private'>{t('breadcrumbHome')}</Link>,
            },
            {
              title: t('breacrumbCurrent'),
            },
          ]}
          style={{ marginBottom: '16px' }}
        />
      </div>
      <IpList />
    </>
  );
};

export default IpOverview;
