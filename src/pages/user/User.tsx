// libs
import React, { useCallback, useState } from 'react';
import { CheckCircleOutlined, CloseCircleOutlined, EditOutlined } from '@ant-design/icons';
import { Breadcrumb, Button, Descriptions, Row, Col, Space, Tag, Card, Divider } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// apis
import { useDisable2FA } from '@/api';

// components
import BindingGA from '@/components/BindingGA';
import MainAccountVertify from '@/components/MainAccountVertify';

// store
import { useNotifyStore, useUserStore } from '@/store';

// pages
import ChangePW from './components/ChangePW';
import SetOpwModal from './components/SetOpwModal';
import ChangeOPW from './components/ChangeOPW';

const User: React.FunctionComponent = () => {
  // states
  const [openChangePW, setOpenChangePW] = useState(false);
  const [openChangeOPW, setOpenChangeOPW] = useState(false);
  const [openBindingGA, setOpenBindingGA] = useState(false);
  const [openSetOpw, setOpenSetOpw] = useState(false);
  const [openDisable2FA, setOpenDisable2FA] = useState(false);

  // hooks
  const { pushBSQ } = useNotifyStore();
  const { info } = useUserStore();
  const { t } = useTranslation('user');

  const { mutate: disable2FA, isPending: isDa } = useDisable2FA({
    onSuccess: () => {
      setOpenDisable2FA(false);
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
    },
    onError: () => {
      setOpenDisable2FA(false);
    },
  });

  // handlers
  const handleDisable2FA = useCallback(
    (values: { operationPassword: string }) => {
      disable2FA({
        OperationPassword: values.operationPassword,
      });
    },
    [disable2FA],
  );

  return (
    <div>
      {/* Breadcrumb */}
      <Breadcrumb
        items={[{ title: <Link to='private'>{t('breadcrumbHome')}</Link> }, { title: t('breadcrumbCurrent') }]}
      />

      {/* Main Card */}
      <Card className='rounded-lg shadow-lg'>
        <Descriptions
          title={t('title')}
          bordered
          column={1}
          labelStyle={{ width: 250 }}
          size='small'
        >
          <Descriptions.Item label={t('accountNameLabel')}>
            <div className='text-lg font-medium tracking-wide'>{info?.nickName}</div>
          </Descriptions.Item>
          <Descriptions.Item label={t('userIdLabel')}>{info?.userId}</Descriptions.Item>
          <Descriptions.Item label={t('permissionsLabel')}>
            <div>{info?.roles[0]}</div>
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Descriptions
          title={t('settingTitle')}
          bordered
          column={1}
          size='small'
          labelStyle={{ width: 250 }}
        >
          <Descriptions.Item label={t('loginPasswordLabel')}>
            <Button
              type='dashed'
              icon={<EditOutlined />}
              onClick={() => setOpenChangePW(true)}
            >
              {t('changePasswordButton')}
            </Button>
          </Descriptions.Item>
          <Descriptions.Item label={t('operationPasswordLabel')}>
            <Row
              gutter={16}
              align='middle'
            >
              <Col>
                {info?.operationPasswordEnabled ? (
                  <Tag
                    icon={<CheckCircleOutlined />}
                    color='success'
                  >
                    {t('activeTag')}
                  </Tag>
                ) : (
                  <Tag
                    icon={<CloseCircleOutlined />}
                    color='default'
                  >
                    {t('notSetTag')}
                  </Tag>
                )}
              </Col>
              <Col>
                <Space>
                  {!info?.operationPasswordEnabled ? (
                    <Button
                      type='dashed'
                      icon={<EditOutlined />}
                      onClick={() => setOpenSetOpw(true)}
                    >
                      {t('settingsButton')}
                    </Button>
                  ) : (
                    <Button
                      type='dashed'
                      icon={<EditOutlined />}
                      onClick={() => setOpenChangeOPW(true)}
                    >
                      {t('changeOperationPasswordButton')}
                    </Button>
                  )}
                </Space>
              </Col>
            </Row>
          </Descriptions.Item>

          <Descriptions.Item label={t('gaVerificationLabel')}>
            <Row
              gutter={16}
              align='middle'
            >
              {info?.twoFactorEnabled ? (
                <>
                  <Col>
                    <Tag
                      icon={<CheckCircleOutlined />}
                      color='success'
                    >
                      {t('activeTag')}
                    </Tag>
                  </Col>
                  <Col>
                    <Button
                      type='dashed'
                      icon={<CloseCircleOutlined />}
                      danger
                      onClick={() => setOpenDisable2FA(true)}
                    >
                      {t('disable2FAButton')}
                    </Button>
                  </Col>
                </>
              ) : (
                <>
                  <Col>
                    <Button
                      type='dashed'
                      icon={<EditOutlined />}
                      onClick={() => setOpenBindingGA(true)}
                    >
                      {t('enable2FAButton')}
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      type='dashed'
                      icon={<CloseCircleOutlined />}
                      disabled
                    >
                      {t('disable2FAButton')}
                    </Button>
                  </Col>
                </>
              )}
            </Row>
          </Descriptions.Item>
        </Descriptions>
        <Divider />
      </Card>

      {/* Modals */}
      <ChangePW
        open={openChangePW}
        setOpen={setOpenChangePW}
      />
      <SetOpwModal
        open={openSetOpw}
        setOpen={setOpenSetOpw}
      />
      <ChangeOPW
        open={openChangeOPW}
        onCancel={() => setOpenChangeOPW(false)}
      />
      <BindingGA
        open={openBindingGA}
        onCancel={() => setOpenBindingGA(false)}
      />

      <MainAccountVertify
        open={openDisable2FA}
        onCancel={() => setOpenDisable2FA(false)}
        onSubmit={handleDisable2FA}
        loading={isDa}
      />
    </div>
  );
};

export default User;
