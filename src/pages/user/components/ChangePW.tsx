// libs
import { But<PERSON>, Divider, Form, Input, Modal } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// apis
import { useChangePWLogin } from '@/api';

// components
import { Title } from '@/components/TypographyMaster';

// utils
import validatePassword from '@/utils';

// store
import { useNotifyStore } from '@/store';

type ChOpwSubmitValues = {
  currentPassword: string;
  newPassword: string;
  readonlyewOperationPassword: string;
};

interface IChangePwProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ChangePw: React.FunctionComponent<IChangePwProps> = (props) => {
  // props
  const { open, setOpen } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('changePW');
  // mutation
  const { mutate: setLgPwCh, isPending: inLgPwCh } = useChangePWLogin({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      setOpen(false);
      form.resetFields();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: ChOpwSubmitValues) => {
      setLgPwCh(values);
    },
    [setLgPwCh],
  );

  return (
    <Modal
      title={
        <Title
          level={3}
          tight
        >
          {t('title')}
        </Title>
      }
      footer={false}
      open={open}
      onCancel={() => setOpen(false)}
      className='translate-y-[5vh]'
    >
      <Form
        {...{ form }}
        variant='filled'
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          name='currentPassword'
          label={t('currentPasswordLabel')}
          rules={[{ required: true, message: t('currentPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inLgPwCh}
            placeholder={t('currentPasswordPlaceholder')}
          />
        </Form.Item>
        <Divider />

        <Form.Item
          name='newPassword'
          label={t('newPasswordLabel')}
          rules={[{ validator: validatePassword }]}
        >
          <Input.Password
            disabled={inLgPwCh}
            placeholder={t('newPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item
          name='readonlyewOperationPassword'
          label={t('confirmNewPasswordLabel')}
          rules={[
            {
              required: true,
              message: t('confirmNewPasswordErrorMessage'),
            },
            {
              validator: (_, value) => {
                const newPassword = form.getFieldValue('newPassword');
                if (newPassword !== value) return Promise.reject(Error(t('passwordsNotMatchMessage')));
                return Promise.resolve();
              },
            },
          ]}
          dependencies={['newPassword']}
        >
          <Input.Password
            disabled={inLgPwCh}
            placeholder={t('confirmNewPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLgPwCh}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ChangePw;
