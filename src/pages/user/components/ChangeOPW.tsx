// libs
import { But<PERSON>, Divider, Form, Input, Modal } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

// apis
import { useChOpw } from '@/api';

// components
import { Title } from '@/components/TypographyMaster';

// store
import { useNotifyStore } from '@/store';

type ChOpwSubmitValues = {
  oldOperationPassword: string;
  newOperationPassword: string;
  readonlyewOperationPassword: string;
};

interface IChangeOPWProps {
  open: boolean;
  onCancel: () => void;
}

const ChangeOPW: React.FunctionComponent<IChangeOPWProps> = (props) => {
  // props
  const { open, onCancel } = props || {};

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('changeOPW');

  // mutation
  const { mutate: chOpw, isPending: inCh } = useChOpw({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      onCancel();
      form.resetFields();
    },
  });

  // handlers
  const handleSubmit = useCallback(
    (values: ChOpwSubmitValues) => {
      const { oldOperationPassword, newOperationPassword } = values;
      chOpw({
        oldOperationPassword,
        newOperationPassword,
      });
    },
    [chOpw],
  );

  return (
    <Modal
      title={
        <Title
          level={3}
          tight
        >
          {t('title')}
        </Title>
      }
      footer={false}
      open={open}
      onCancel={onCancel}
      className='translate-y-[5vh]'
    >
      <Form
        {...{ form }}
        variant='filled'
        onFinish={handleSubmit}
        layout='vertical'
      >
        <Form.Item
          name='oldOperationPassword'
          label={t('oldOperationPasswordLabel')}
          rules={[{ required: true, message: t('oldOperationPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('oldOperationPasswordPlaceholder')}
          />
        </Form.Item>
        <Divider />

        <Form.Item
          name='newOperationPassword'
          label={t('newOperationPasswordLabel')}
          rules={[{ required: true, message: t('newOperationPasswordErrorMessage') }]}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('newOperationPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item
          name='readonlyewOperationPassword'
          label={t('readonlyNewOperationPasswordLabel')}
          rules={[
            {
              required: true,
              message: t('readonlyewOperationPasswordErrorMessage'),
            },
            {
              validator: (_, value) => {
                const newOperationPassword = form.getFieldValue('newOperationPassword');
                if (newOperationPassword !== value) return Promise.reject(Error('passwordNotMatchMessage'));
                return Promise.resolve();
              },
            },
          ]}
          dependencies={['newOperationPassword']}
        >
          <Input.Password
            disabled={inCh}
            placeholder={t('readonlyNewOperationPasswordPlaceholder')}
          />
        </Form.Item>

        <Form.Item className='mb-0'>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inCh}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ChangeOPW;
