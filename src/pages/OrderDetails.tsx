// libs
import { CheckOutlined, ContainerOutlined } from '@ant-design/icons';
import { Avatar, Button, Col, Descriptions, DescriptionsProps, Modal, Row, Space } from 'antd';
import * as React from 'react';
import { useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// apis
import { OderListMerchantOptions } from '@/api';

// assets
import usdt from '@/assets/usdt.png';

// components
import { Txt } from '@/components/TypographyMaster';
import { TagTxStatus } from '@/components/TagAlpha';

// hooks
import { dateFormator } from '@/hook';

// utils
import { CryptoEnum, cryptoEnumOptions, nTot, valueToLabel } from '@/utils';

// pages
import DepositVertify from '@/pages/deposit/components/DepositVertify';

interface IDepositDetailProps {
  order: OderListMerchantOptions | undefined;
  setOrder: React.Dispatch<React.SetStateAction<OderListMerchantOptions | undefined>>;
}

const OrderDetails: React.FunctionComponent<IDepositDetailProps> = (props) => {
  // props
  const { order, setOrder } = props || {};

  // states
  const [openDepositVertify, setOpenDepositVertify] = useState(false);

  // hooks
  const { t } = useTranslation('orderDetails');

  // handlers
  const handleSubmit = () => {};

  const items: DescriptionsProps['items'] = useMemo(() => {
    if (!order) return [];
    const avatarSrc = (() => {
      if (order.order.cryptoType === CryptoEnum.TRC20_USDT) return usdt;
      if (order.order.cryptoType === CryptoEnum.ERC20_USDT) return usdt;
      return '';
    })();

    // eslint-disable-next-line no-nested-ternary
    const chainDetailURL = order.order.hash
      ? import.meta.env.MODE === 'development'
        ? `https://nile.tronscan.org/#/transaction/${order.order.hash}`
        : `https://tronscan.org/#/transaction/${order.order.hash}`
      : '--';

    return [
      {
        key: '1',
        label: t('orderType'),
        children: <div className='text-lg font-bold'>{t('memberOrderCreation')}</div>,
        span: 2,
      },
      {
        key: '2',
        label: t('merchantOrderNumber'),
        children: <Txt copyable>{order.merchantOrderId}</Txt>,
        span: 2,
      },
      {
        key: '3',
        label: t('orderId'),
        children: <Txt copyable>{order.orderUid}</Txt>,
        span: 2,
      },
      {
        key: '4',
        label: t('createdAt'),
        children: dayjs(order.createdAt).format(dateFormator.accurate),
      },
      {
        key: '5',
        label: t('confirmedAt'),
        children: order.order.confirmedAt ? dayjs(order.order.confirmedAt).format(dateFormator.accurate) : '--',
      },
      {
        key: '6',
        label: t('status'),
        children: (
          <div className='flex justify-between'>
            <TagTxStatus status={order.order.status} />
          </div>
        ),
        span: 2,
      },
      {
        key: '7',
        label: t('applicant'),
        children: order.merchantUserName ? order.merchantUserName : '--',
        span: 2,
      },
      {
        key: '8',
        label: t('requireAmount'),
        children: <Txt>{nTot({ value: order.order.requireAmount, digitsType: 'USDT' })}</Txt>,
      },
      {
        key: '9',
        label: t('currency'),
        children: (
          <Space>
            <Avatar
              size='small'
              src={avatarSrc}
            />
            <Txt>{valueToLabel(order.order.cryptoType, cryptoEnumOptions)}</Txt>
          </Space>
        ),
      },
      {
        key: '10',
        label: t('fees'),
        row: 1,
        children: (
          <div className='flex flex-col text-left'>
            <Space>
              <Txt>- {t('fee')}:</Txt>
              <Txt>{nTot({ value: order.order.fee, digitsType: 'USDT' })}</Txt>
            </Space>

            <Space>
              <Txt>- {t('gas')}:</Txt>
              <Txt>{nTot({ value: order.order.gas, digitsType: 'USDT' })}</Txt>
            </Space>
          </div>
        ),
      },
      {
        key: '11',
        label: t('protocol'),
        children: 'TRC20',
      },
      {
        key: '12',
        label: t('actualAmount'),
        children: <Txt>{nTot({ value: order.order.actualAmount, digitsType: 'USDT' })}</Txt>,
        span: 2,
      },
      {
        key: '13',
        label: t('to'),
        children: <Txt copyable>{order.order.to}</Txt>,
        span: 2,
      },
      {
        key: '14',
        label: t('publicChainDetails'),
        children: (
          <Button
            type='link'
            size='small'
            onClick={() => {
              window.open(chainDetailURL, '_blank');
            }}
            className='px-0'
            disabled={!order.order.hash}
          >
            {chainDetailURL}
          </Button>
        ),
        span: 2,
      },
      {
        key: '15',
        label: t('remark'),
        children: <Txt copyable>{order.order.remark}</Txt>,
        span: 2,
      },
      {
        key: '16',
        label: t('log'),
        children: <div />,
      },
    ];
  }, [order, t]);
  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={!!order}
        onCancel={() => setOrder(undefined)}
        width='fit-content'
      >
        <Descriptions
          size='small'
          bordered
          items={items}
          column={2}
          className='mb-8'
        />
        {/* if狀態=驗證中 */}

        <Row gutter={10}>
          <Col flex='auto'>
            <Button
              icon={<CheckOutlined />}
              type='primary'
              block
              onClick={() => setOpenDepositVertify(true)}
              disabled
            >
              {t('verifiedButton')}
            </Button>
          </Col>
          <Col flex='auto'>
            <Button
              icon={<ContainerOutlined />}
              type='primary'
              ghost
              block
              danger
              onClick={() => setOpenDepositVertify(true)}
              disabled
            >
              {t('determinedButton')}
            </Button>
          </Col>
        </Row>
      </Modal>
      <DepositVertify
        open={openDepositVertify}
        onCancel={() => setOpenDepositVertify(false)}
        loading={false}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default OrderDetails;
