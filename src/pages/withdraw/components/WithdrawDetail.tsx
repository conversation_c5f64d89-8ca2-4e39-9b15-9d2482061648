// libs
import { CheckOutlined, ContainerOutlined } from '@ant-design/icons';
import { Avatar, Button, Col, Descriptions, DescriptionsProps, Modal, Row, Space } from 'antd';
import * as React from 'react';
import dayjs from 'dayjs';

// assets
import usdt from '@/assets/usdt.png';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

// api
import { OderListMerchantOptions } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';
import { TagTxStatus } from '@/components/TagAlpha';

// hooks
import { dateFormator } from '@/hook';

// utils
import { CryptoEnum, cryptoEnumOptions, nTot, valueToLabel } from '@/utils';

// pages
import WithdrawVertify from '@/pages/withdraw/components/WithdrawVertify';

interface IWithdrawDetailProps {
  order: OderListMerchantOptions | undefined;
  setOrder: React.Dispatch<React.SetStateAction<OderListMerchantOptions | undefined>>;
}

const WithdrawDetail: React.FunctionComponent<IWithdrawDetailProps> = (props) => {
  // props
  const { order, setOrder } = props || {};

  // states
  const [openWithdrawVertify, setOpenWithdrawVertify] = useState(false);

  // hooks
  const { t } = useTranslation('withdrawDetail');

  // compute
  const items: DescriptionsProps['items'] = useMemo(() => {
    if (!order) return [];
    const avatarSrc = (() => {
      if (order.order.cryptoType === CryptoEnum.TRC20_USDT) return usdt;
      if (order.order.cryptoType === CryptoEnum.ERC20_USDT) return usdt;
      return '';
    })();

    return [
      {
        key: '1',
        label: t('orderType'),
        children: <div className='text-lg font-bold'>{t('deposit')}</div>,
        span: 2,
      },
      {
        key: '2',
        label: t('orderNumber'),
        children: t('notAvailable'),
      },
      {
        key: '3',
        label: t('serialNumber'),
        children: t('notAvailable'),
      },
      {
        key: '4',
        label: t('from'),
        children: <Space>{order.order.from ? <Txt copyable>{order.order.from}</Txt> : <Txt>--</Txt>}</Space>,
      },
      {
        key: '5',
        label: t('to'),
        children: <Space>{order.order.to ? <Txt copyable>{order.order.to}</Txt> : <Txt>--</Txt>}</Space>,
      },
      {
        key: '6',
        label: t('createdAt'),
        children: dayjs(order.createdAt).format(dateFormator.accurate),
      },
      {
        key: '7',
        label: t('confirmedAt'),
        children: order.order.confirmedAt ? dayjs(order.order.confirmedAt).format(dateFormator.accurate) : '--',
      },
      {
        key: '8',
        label: t('status'),
        children: (
          <div className='flex justify-between'>
            <TagTxStatus status={order.order.status} />
          </div>
        ),
        span: 2,
      },
      {
        key: '9',
        label: t('applicant'),
        children: order.merchantUserName ? order.merchantUserName : '--',
        span: 2,
      },
      {
        key: '10',
        label: t('requireAmount'),
        children: <Txt>{nTot({ value: order.order.requireAmount, digitsType: 'USDT' })}</Txt>,
      },
      {
        key: '11',
        label: t('currency'),
        children: (
          <Space>
            <Avatar
              size='small'
              src={avatarSrc}
            />
            <Txt>{valueToLabel(order.order.cryptoType, cryptoEnumOptions)}</Txt>
          </Space>
        ),
      },
      {
        key: '12',
        label: t('fees'),
        row: 1,
        children: (
          <div className='flex flex-col text-left'>
            <Space>
              <Txt>- {t('fee')}:</Txt>
              <Txt>{nTot({ value: order.order.fee, digitsType: 'USDT' })}</Txt>
            </Space>

            <Space>
              <Txt>- {t('gas')}:</Txt>
              <Txt>{nTot({ value: order.order.gas, digitsType: 'USDT' })}</Txt>
            </Space>
          </div>
        ),
      },
      {
        key: '13',
        label: t('protocol'),
        children: 'TRC20',
      },
      {
        key: '14',
        label: t('actualAmount'),
        children: <Txt>{nTot({ value: order.order.actualAmount, digitsType: 'USDT' })}</Txt>,
        span: 2,
      },

      {
        key: '15',
        label: t('publicChainDetails'),
        children: (
          <Button
            type='link'
            size='small'
          >
            https://tronscan.org/#/transaction/0dcaf998c364a18f5d7d10e22f8fdb453d33eed0a92871c9caf25195ee036472
          </Button>
        ),
        span: 2,
      },
      {
        key: '16',
        label: t('remark'),
        children: <Txt copyable>{order.order.remark}</Txt>,
        span: 2,
      },
      {
        key: '17',
        label: t('log'),
        children: <div />,
      },
    ];
  }, [order, t]);
  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={!!order}
        onCancel={() => setOrder(undefined)}
        width='fit-content'
      >
        <Descriptions
          size='small'
          bordered
          items={items}
          column={2}
          className='mb-8'
        />
        {/* if狀態=驗證中 */}

        <Row gutter={10}>
          <Col flex='auto'>
            <Button
              icon={<CheckOutlined />}
              type='primary'
              block
              onClick={() => setOpenWithdrawVertify(true)}
            >
              {t('verifyButton')}
            </Button>
          </Col>
          <Col flex='auto'>
            <Button
              icon={<ContainerOutlined />}
              type='primary'
              ghost
              block
              danger
              onClick={() => setOpenWithdrawVertify(true)}
            >
              {t('determinedButton')}
            </Button>
          </Col>
        </Row>
      </Modal>
      <WithdrawVertify
        open={openWithdrawVertify}
        onCancel={() => setOpenWithdrawVertify(false)}
      />
    </div>
  );
};

export default WithdrawDetail;
