import { LockOutlined } from '@ant-design/icons';
// libs
import { Button, Form, Input, Modal } from 'antd';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

interface IWithdrawVertifyProps {
  open: boolean;
  onCancel: () => void;
}

const WithdrawVertify: React.FunctionComponent<IWithdrawVertifyProps> = ({ open, onCancel }) => {
  // hooks
  const { t } = useTranslation('withdrawVerify');

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={open}
      onCancel={onCancel}
      centered
    >
      <Form
        name='verifyForm'
        variant='filled'
        layout='vertical'
        initialValues={{ remember: true }}
      >
        <Form.Item
          label={t('passwordLabel')}
          name='password'
        >
          <Input.Password
            prefix={<LockOutlined />}
            type='password'
            placeholder={t('passwordPlaceholder')}
          />
        </Form.Item>
        <Form.Item
          label={t('gaVerificationLabel')}
          name='gaVerification'
        >
          <Input.OTP variant='filled' />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WithdrawVertify;
