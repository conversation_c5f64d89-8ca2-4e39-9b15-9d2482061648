import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Flex, Select } from 'antd';
import { Link } from 'react-router-dom';
import { useCallback, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { OderListMerchantOptions, useMemberOderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import BtnFuncs from '@/components/BtnFuncs';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import {
  cryptoEnumOptions,
  exportSheetByArray,
  storageHelper,
  TxCategoryNum,
  txCategoryNumOptions,
  TxStatusNum,
  txStatusOptions,
} from '@/utils';
import { Txt } from '@/components/TypographyMaster';
import useWithdrawMerchantColumns from './useWithdrawMerchantColumns';
import WithdrawVertify from './components/WithdrawVertify';
import WithdrawDetail from '../OrderDetails';

interface IWithdrawProps {
  isActive?: boolean;
}

const Withdraw: React.FunctionComponent<IWithdrawProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const storageRange = storageHelper<{ from: string; to: string }>('withdrawalRange').getItem();
  const { t } = useTranslation('withdraw');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'withdraw' });
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };

  // states
  const [openWithdrawVertify, setOpenWithdrawVertify] = useState(false);
  const [withdrawtMatchFrom, setWithdrawMatchFrom] = useState<OderListMerchantOptions>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [OrderUid, setOrderUid] = useState<string>();
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [Hash, setHash] = useState<string>();
  const [Status, setStatus] = useState<Array<TxStatusNum>>();

  const { data, isPending, isRefetching } = useMemberOderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      TransactionType: TxCategoryNum.Withdraw,
      IsUncompleted: true,
      CreatedAtStart: dateRange.from.format(),
      CreatedAtEnd: dateRange.to.format(),
      OrderUid,
      MerchantOrderId,
      Hash,
      Status,
    },
  });

  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);
  const { columns } = useWithdrawMerchantColumns({ data, setWithdrawMatchFrom });

  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('withdrawalRange').setItem(newDate);
  }, []);
  const translateTxStatusOptions = useMemo(
    () => txStatusOptions.map((option) => ({ ...option, label: optionsT(option.label) })),
    [optionsT],
  );

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex
        justify='space-between'
        className='my-5 gap-x-2'
      >
        {/* <Space>
          <Tooltip title='批量驗證，請勾選欲驗證的訂單'>
            <Button
              icon={<CheckOutlined />}
              shape='round'
              type='primary'
              onClick={() => setOpenWithdrawVertify(true)}
            >
              已驗證
            </Button>
          </Tooltip>
          <Tooltip title='批量駁回，請勾選欲駁回的訂單'>
            <Button
              icon={<CloseOutlined />}
              shape='round'
              type='primary'
              danger
              disabled
            >
              駁回
            </Button>
          </Tooltip>
        </Space> */}
        <Flex
          gap={10}
          className='flex items-center justify-between'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
        </Flex>
        <BtnFuncs
          iconType='print'
          loading={isRefetching}
          onClick={() => {
            const sheetTitleRow = [
              'merchantNumberTitleRow',
              'merchantNameTitleRow',
              'typeTitleRow',
              'createdAtTitleRow',
              'confirmedAtTitleRow',
              'orderUidTitleRow',
              'merchantOrderNumberTitleRow',
              'transactionHashTitleRow',
              'fromAddressTitleRow',
              'toAddressTitleRow',
              'cryptoTitleRow',
              'consumedTitleRow',
              'feeTitleRow',
              'requiredTitleRow',
              'actualTitleRow',
              'transferTitleRow',
              'statusTitleRow',
              'remarkTitleRow',
            ];
            const sheetDataRows = [
              ...dataSource.map((deposit) => {
                const transactionType = txCategoryNumOptions.find(
                  (findO) => findO.value === deposit.order.transactionType,
                );
                const cryptoType = cryptoEnumOptions.find((findO) => findO.value === deposit.order.cryptoType);
                const statusOption = txStatusOptions.find((findO) => findO.value === deposit.order.status);
                const createdAt = deposit.createdAt ? dayjs(deposit.createdAt).format(dateFormator.accurate) : '';
                const confirmedAt = deposit.order.confirmedAt
                  ? dayjs(deposit.order.confirmedAt).format(dateFormator.accurate)
                  : '';
                return [
                  deposit.merchantNumber,
                  deposit.merchantName,
                  optionsT(transactionType?.label || 'undefined'),
                  createdAt,
                  confirmedAt,
                  deposit.orderUid,
                  deposit.merchantOrderId,
                  deposit.order.hash,
                  deposit.order.from,
                  deposit.order.to,
                  optionsT(cryptoType?.label || 'undefined'),
                  deposit.order.gas,
                  deposit.order.fee,
                  deposit.order.requireAmount,
                  deposit.order.actualAmount,
                  deposit.order.requireAmount - deposit.order.fee,
                  optionsT(statusOption?.label || 'undefined'),
                  deposit.order.remark,
                ];
              }),
            ];

            exportSheetByArray({
              arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
              sheetName: t('fileName'),
              fileName: `${t('fileName')} ${dayjs().format(dateFormator.accurate)}`,
            });
          }}
        />
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='orderUid'
        titleRender={
          <div className='flex flex-wrap items-center gap-x-2'>
            <div className='flex flex-col gap-y-1'>
              <Txt
                type='secondary'
                className='font-bold'
              >
                {t('statusLabel')}:
              </Txt>
              <Select
                placeholder={t('statusPlaceholder')}
                variant='filled'
                className='w-[160px]'
                options={translateTxStatusOptions}
                disabled={isRefetching}
                onChange={(newValue) => setStatus(newValue)}
                allowClear
              />
            </div>
            <SearchMaster
              titles={[
                { key: 'OrderUid', label: t('orderUidLabel') },
                { key: 'MerchantOrderId', label: t('merchantOrderIdLabel') },
                { key: 'Hash', label: t('hashLabel') },
              ]}
              onSearch={(values) => {
                setOrderUid(values.OrderUid || undefined);
                setMerchantOrderId(values.MerchantOrderId || undefined);
                setHash(values.Hash || undefined);
              }}
            />
          </div>
        }
      />
      <WithdrawDetail
        order={withdrawtMatchFrom}
        setOrder={setWithdrawMatchFrom}
      />
      <WithdrawVertify
        open={openWithdrawVertify}
        onCancel={() => setOpenWithdrawVertify(false)}
      />
    </div>
  );
};

export default Withdraw;
