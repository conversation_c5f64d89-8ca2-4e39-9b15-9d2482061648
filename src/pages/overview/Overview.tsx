// libs
import * as React from 'react';
import { Row, Col, Space, Avatar, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { SmileOutlined } from '@ant-design/icons';

// api
import { useMcDetails } from '@/api';

// assets
import usdt from '@/assets/usdt.png';

// store
import { useUserStore } from '@/store';

// pages
import BalanceCard from './BalanceCard';

interface IOverviewProps {}

const Overview: React.FunctionComponent<IOverviewProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { info } = useUserStore();
  const { data: balaneOVR } = useMcDetails({});
  const { t } = useTranslation('overview');

  return (
    <div className='m-auto max-w-[1280px]'>
      <Row gutter={[8, 8]}>
        <Col
          span={24}
          className='my-5 text-center'
        >
          <Space
            direction='vertical'
            size='middle'
            className='text-center'
          >
            <SmileOutlined className='text-7xl text-[#52c41a]' />
            <Typography.Title level={2}>
              {t('title')} <span className='text-[#52c41a]'>{info?.nickName}</span>
            </Typography.Title>
          </Space>
        </Col>
        <Col
          span={24}
          className='my-3 text-center'
        >
          <Avatar
            src={usdt}
            size={42}
          />{' '}
        </Col>
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <BalanceCard
                title={t('firstCardTitle')}
                value={balaneOVR?.wallet?.balance}
                color='#52c41a'
              />
            </Col>
            <Col span={12}>
              <BalanceCard
                title={t('secondCardTitle')}
                value={balaneOVR?.wallet?.lockedBalance}
                color='#ff4d4f'
              />
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default Overview;
