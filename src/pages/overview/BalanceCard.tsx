// libs
import React from 'react';
import { Card, Statistic } from 'antd';
import { useTranslation } from 'react-i18next';

// store
import { useThemeStore } from '@/store/useThemeStore';

interface BalanceCardProps {
  title: string;
  value: number | string | undefined;
  color: string;
}

const BalanceCard: React.FC<BalanceCardProps> = ({ title, value, color }) => {
  // hooks
  const { isWhite } = useThemeStore();
  const dynamicColor = isWhite ? color : color;
  const { t } = useTranslation('balanceCard');

  return (
    <Card
      bordered={false}
      style={{
        borderRadius: '12px',
        boxShadow: isWhite ? '0 2px 8px rgba(0, 0, 0, 0.1)' : `0 2px 8px ${color}40`,
      }}
      title={<span className='font-bold tracking-widest'>{title}</span>}
    >
      <Statistic
        value={value}
        valueStyle={{
          fontWeight: 'bold',
          color: dynamicColor,
        }}
        suffix={<div className='text-sm'>{t('currency')}</div>}
      />
    </Card>
  );
};

export default BalanceCard;
