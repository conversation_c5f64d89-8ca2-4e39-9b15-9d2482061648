import { LedgerItemInterface, useLedgerDetails } from '@/api/ledger';
import ModalAlpha from '@/components/ModalAlpha';
import { Title } from '@/components/TypographyMaster';
import { useTranslation } from 'react-i18next';
import LedgerDetails from './LedgerDetails';

interface LedgerDetailsModalProps {
  openFrom: LedgerItemInterface | undefined;
  setOpenFrom: ReactSet<LedgerDetailsModalProps['openFrom']>;
}
const LedgerDetailsModal = (props: LedgerDetailsModalProps) => {
  const { openFrom, setOpenFrom } = props || {};

  const { t } = useTranslation('ledgerDetailsModal');
  const {
    data: ledgerDetails,
    isSuccess,
    isPending: loadingLedgerDetails,
  } = useLedgerDetails({
    params: {
      id: openFrom?.id,
    },
    enabled: !!openFrom?.id,
  });

  const handleCancel = () => {
    setOpenFrom(undefined);
  };

  return (
    <ModalAlpha
      open={!!openFrom}
      onCancel={handleCancel}
      footer={null}
      width='fit-content'
      title={<Title level={3}>{t('title')}</Title>}
    >
      <LedgerDetails
        ledger={isSuccess ? ledgerDetails : undefined}
        loading={loadingLedgerDetails}
      />
    </ModalAlpha>
  );
};

export default LedgerDetailsModal;
