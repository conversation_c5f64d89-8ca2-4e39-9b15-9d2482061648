import { LedgerItemInterface, useLedger } from '@/api/ledger';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import { useUserStore } from '@/store';
import { cryptoEnumOptions, exportSheetByArray, ledgerSourceEnumOptions, storageHelper } from '@/utils';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import BtnFuncs from '@/components/BtnFuncs';
import DateRange from '@/components/DateRange';
import { Breadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import TableAlpha from '@/components/TableAlpha';
import useLedgerColumns from './useLedgerColumns';
import LedgerDetailsModal from './LedgerDetailsModal';

const storageRange = storageHelper<{ from: string; to: string }>('depositRange').getItem();
const defaultDateRange = storageRange
  ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
  : { from: dayjs().startOf('d'), to: dayjs().startOf('d').add(1, 'd') };

type DateRangeOptions = { from: Dayjs; to: Dayjs };

interface ILedger {}

const Ledger: React.FunctionComponent<ILedger> = (props) => {
  const {} = props || {};

  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange); // page filters
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({}); // page filters
  const [showLedgerFrom, setShowLedgerFrom] = useState<LedgerItemInterface | undefined>();

  const { isDark } = useUserStore();
  const { t } = useTranslation('ledger');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'ledger' });

  const { data, isPending, isRefetching, isError } = useLedger({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      StartTime: dateRange.from.format(),
      EndTime: dateRange.to.format(),
    },
  });

  // compute
  const dataSource = useMemo(() => {
    if (!data || isError) return [];
    return data.items;
  }, [data, isError]);
  const { columns } = useLedgerColumns({
    setShowLedgerFrom,
  });

  // handlers
  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('depositRange').setItem(newDate);
  }, []);

  const handleExportXlsx = useCallback(() => {
    const sheetTitleRow = [
      'relatedOrderNoTitleRow',
      'createTimeTitleRow',
      'cryptoTypeTitleRow',
      'amountTitleRow',
      'feeTitleRow',
      'balanceTitleRow',
      'sourceTitleRow',
    ];
    const sheetDataRows = [
      ...dataSource.map((mapL) => {
        const cryptoType = cryptoEnumOptions.find((findO) => findO.value === mapL.cryptoType);
        const sourceOption = ledgerSourceEnumOptions.find((findO) => findO.value === mapL.source);
        const createTime = mapL.createdAt ? dayjs(mapL.createdAt).format(dateFormator.accurate) : '';

        return [
          mapL.relatedOrderNo,
          createTime,
          optionsT(cryptoType?.label || ''),
          mapL.amount,
          mapL.totalFee,
          mapL.balanceAfter,
          optionsT(sourceOption?.label || 'undefined'),
        ];
      }),
    ];
    exportSheetByArray({
      arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
      sheetName: t('sheetName'),
      fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
        dateFormator.accurate,
      )} `,
    });
  }, [dataSource, dateRange.from, dateRange.to, translateExcelTitleRow, t, optionsT]);

  return (
    <>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('homeBreadcrumb')}</Link>,
          },
          {
            title: t('currentBreadcrumb'),
          },
        ]}
      />

      <header className='my-4 flex gap-y-2 justify-between'>
        <DateRange
          loading={isRefetching}
          onDateSubmit={handleOnDateSubmit}
          defaultValues={dateRange}
        />
        <BtnFuncs
          iconType='print'
          loading={isRefetching}
          onClick={handleExportXlsx}
        />
      </header>

      {/* main */}
      <TableAlpha
        {...{
          isDark,
          dataSource,
          columns,
          totalDataLength: data?.totalCount,
          pageSize,
          setPageSize,
          currentPage,
          setCurrentPage,
        }}
        size='small'
        rowKey='id'
        loading={isPending}
      />

      {/* Dialogue */}
      <LedgerDetailsModal
        openFrom={showLedgerFrom}
        setOpenFrom={setShowLedgerFrom}
      />
    </>
  );
};

export default Ledger;
