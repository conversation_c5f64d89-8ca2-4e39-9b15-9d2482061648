import { useMemo } from 'react';
import { Descriptions, DescriptionsProps, Flex, Skeleton } from 'antd';
import { useTranslation } from 'react-i18next';
import { Title, Txt } from '@/components/TypographyMaster';
import { LedgerDetailsRes } from '@/api/ledger';
import { TagCryptoType, TagDIOrderStatus, TagLedgerSource, TagLedgerTransactionType } from '@/components/TagAlpha';
import { nTot } from '@/utils';
import dayjs from 'dayjs';
import { BtnCopy } from '@/components/BtnFuncs';
import { dateFormator } from '@/hook';

interface LedgerDetailsProps {
  ledger: LedgerDetailsRes | undefined;
  loading: boolean;
}

const LedgerDetails = ({ ledger, loading }: LedgerDetailsProps) => {
  const { t } = useTranslation('ledgerDetails');

  const items: DescriptionsProps['items'] = useMemo(() => {
    if (loading)
      return Array.from({ length: 10 }, (_, i) => ({
        key: `skeleton-${i}`,
        label: <Txt>—</Txt>,
        children: <Skeleton.Input active />,
        span: 2,
      }));

    if (ledger)
      return [
        {
          key: 'source',
          label: <Txt>{t('source')}</Txt>,
          children: <TagLedgerSource source={ledger.source} />,
          span: 2,
        },
        {
          key: 'orderUid',
          label: <Txt>{t('orderNumber')}</Txt>,
          children: (
            <Flex
              align='center'
              gap={4}
            >
              <Txt>{ledger.detail.orderUid}</Txt>
              <BtnCopy text={ledger.detail.orderUid} />
            </Flex>
          ),
          span: 2,
        },
        {
          key: 'merchantOrderId',
          label: <Txt>{t('merchantOrderNumber')}</Txt>,
          children: <Txt>{ledger.detail.merchantOrderId}</Txt>,
          span: 2,
        },
        {
          key: 'memberId',
          label: <Txt>{t('memberId')}</Txt>,
          children: <Txt>{ledger.detail.memberId}</Txt>,
          span: 2,
        },
        {
          key: 'payerBankAccountName',
          label: <Txt>{t('payerBankAccountName')}</Txt>,
          children: <Txt>{ledger.detail.payerBankAccountName || '--'}</Txt>,
          span: 2,
        },
        {
          key: 'entryCode',
          label: <Txt>{t('entryCode')}</Txt>,
          children: <Txt>{ledger.detail.entryCode}</Txt>,
          span: 2,
        },
        {
          key: 'transactionType',
          label: <Txt>{t('transactionType')}</Txt>,
          children: <TagLedgerTransactionType type={ledger.detail.transactionType} />,
          span: 2,
        },
        {
          key: 'crypto',
          label: <Txt>{t('crypto')}</Txt>,
          children: (
            <Flex
              align='center'
              justify='space-between'
              gap={4}
            >
              <Txt>{nTot({ value: ledger.detail.cryptoAmount, digitsType: 'USDT' })}</Txt>
              <TagCryptoType cryptoType={ledger.detail.cryptoType} />
            </Flex>
          ),
          span: 2,
        },
        {
          key: 'fiat',
          label: <Txt>{t('fiat')}</Txt>,
          children: (
            <Flex
              align='center'
              justify='space-between'
              gap={4}
            >
              <Txt>{nTot({ value: ledger.detail.fiatAmount, digitsType: 'CNY' })}</Txt>
              <Txt>{ledger.detail.fiatType}</Txt>
            </Flex>
          ),
          span: 2,
        },
        {
          key: 'totalFee',
          label: <Txt>{t('totalFee')}</Txt>,
          children: <Txt>{nTot({ value: ledger.detail.totalFee })}</Txt>,
          span: 2,
        },
        {
          key: 'status',
          label: <Txt>{t('status')}</Txt>,
          children: <TagDIOrderStatus status={ledger.detail.status} />,
          span: 2,
        },
        {
          key: 'createdAt',
          label: <Txt>{t('createdAt')}</Txt>,
          children: <Txt>{dayjs(ledger.detail.createdAt).format(dateFormator.accurate)}</Txt>,
          span: 2,
        },
        {
          key: 'isMerchantNotified',
          label: <Txt>{t('isMerchantNotified')}</Txt>,
          children: (
            <Txt className={ledger.detail.isMerchantNotified ? 'text-green-600' : 'text-red-600'}>
              {ledger.detail.isMerchantNotified ? t('notified') : t('notNotified')}
            </Txt>
          ),
          span: 2,
        },
      ];

    return [];
  }, [ledger, loading, t]);

  if (!ledger && !loading)
    return (
      <main>
        <Title
          level={3}
          type='warning'
        >
          {t('noDataTitle')}
        </Title>
        <Txt>{t('noDataDescription')}</Txt>
      </main>
    );

  return (
    <Descriptions
      className='pt-5'
      size='small'
      bordered
      items={items}
      column={2}
    />
  );
};

export default LedgerDetails;
