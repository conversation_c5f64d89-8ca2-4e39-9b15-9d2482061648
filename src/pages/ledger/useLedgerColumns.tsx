import { useCallback, useMemo, useState } from 'react';
import { TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { useUserStore } from '@/store';
import { nTot } from '@/utils';
import { LedgerItemInterface } from '@/api/ledger';
import { TagCryptoType, TagLedgerSource } from '@/components/TagAlpha';
import { dateFormator } from '@/hook';

type UseProps = {
  setShowLedgerFrom: ReactSet<LedgerItemInterface | undefined>;
};

const useLedgerColumns = (useProps: UseProps) => {
  const { setShowLedgerFrom } = useProps;

  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  const { isDark } = useUserStore();
  const { t } = useTranslation('useLedgerColumns');

  const onClickStatus = useCallback(
    (row: LedgerItemInterface) => {
      setShowLedgerFrom(row);
    },
    [setShowLedgerFrom],
  );

  // compute
  const columns = useMemo(() => {
    const result: TableColumnsType<LedgerItemInterface> = [
      {
        title: <Txt>{t('relatedOrderNo')}</Txt>,
        key: 'relatedOrderNo',
        render: (_, { relatedOrderNo }) => {
          const isExpanded = expandedKeys.includes(relatedOrderNo.toString() || '');
          const isHovered = hoveredKeys.includes(relatedOrderNo.toString() || '');

          return (
            <TxtCompressible
              {...{ isWhite: !isDark, isHovered, isExpanded }}
              text={relatedOrderNo.toString() || ''}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
            />
          );
        },
      },
      {
        title: <Txt>{t('createdAt')}</Txt>,
        key: 'createdAt',
        render: (_, { createdAt }) => {
          const createdAtFormat = createdAt ? dayjs(createdAt).format(dateFormator.accurate) : '--';

          return <Txt>{createdAtFormat}</Txt>;
        },
      },
      {
        title: <Txt>{t('cryptoColumn')}</Txt>,
        key: 'cryptoType',
        align: 'center',
        render: (_, { cryptoType }) => {
          return <TagCryptoType {...{ cryptoType }} />;
        },
      },
      {
        title: <Txt>{t('amount')}</Txt>,
        key: 'amount',
        align: 'right',
        sorter: true,
        render: (_, { amount }) => {
          return <Txt>{nTot({ value: amount, digitsType: 'USDT' })}</Txt>;
        },
      },
      {
        title: <Txt>{t('totalFee')}</Txt>,
        key: 'totalFee',
        align: 'right',
        sorter: true,
        render: (_, { totalFee }) => {
          return <Txt>{totalFee ? nTot({ value: totalFee, digitsType: 'USDT' }) : '--'}</Txt>;
        },
      },
      {
        title: <Txt>{t('balanceAfter')}</Txt>,
        key: 'balanceAfter',
        align: 'right',
        sorter: true,
        render: (_, { balanceAfter }) => {
          return <Txt>{balanceAfter ? nTot({ value: balanceAfter, digitsType: 'USDT' }) : '--'}</Txt>;
        },
      },
      {
        title: <Txt>{t('source')}</Txt>,
        key: 'source',
        align: 'center',
        render: (_, record) => {
          const { source } = record;

          return (
            <TagLedgerSource
              {...{ source }}
              onClick={() => onClickStatus(record)}
              className='cursor-pointer'
              tooltip={t('viewDetails')}
            />
          );
        },
      },
    ];

    return result;
  }, [expandedKeys, hoveredKeys, isDark, onClickStatus, t]);

  return { columns };
};

export default useLedgerColumns;
