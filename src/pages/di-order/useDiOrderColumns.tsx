import { useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { Avatar, Space, TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';
import { DiOrderOptions, DiOrderListRes } from '@/api';
import usdt from '@/assets/usdt.png';
import { Txt, TxtCompressible } from '@/components/TypographyMaster';
import { TagDIOrderStatus, TagDiOrderType } from '@/components/TagAlpha';
import { cryptoEnumOptions, nTot } from '@/utils';
import { useThemeStore } from '@/store/useThemeStore';

const diUrlBase = decodeURIComponent(import.meta.env.VITE_DI_URL || 'https://k28.uxm-pay.uk/#/external/');
type DiOrderColumnsProps = {
  data: DiOrderListRes | undefined;
  setDiOrderMatchFrom: React.Dispatch<React.SetStateAction<DiOrderOptions | undefined>>;
};

const useDiOrderColumns = (useProps: DiOrderColumnsProps) => {
  // props
  const {} = useProps;

  // states
  const [hoveredKeys, setHoveredKeys] = useState<Array<string>>([]);
  const [expandedKeys, setExpandedKeys] = useState<Array<string>>([]);

  // hooks
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('useDiOrderColumns');

  const columns = useMemo(() => {
    const result: TableColumnsType<DiOrderOptions> = [
      {
        key: 'orderUid',
        dataIndex: 'orderUid',
        title: <Txt>{t('orderUid')}</Txt>,
        align: 'center',
        render: (_, { orderUid }) => {
          const isExpanded = expandedKeys.includes(orderUid || '');
          const isHovered = hoveredKeys.includes(orderUid || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={orderUid}
            />
          );
        },
      },
      {
        key: 'redirectUrl',
        dataIndex: 'redirectUrl',
        title: <Txt>{t('redirectUrl')}</Txt>,
        align: 'center',
        render: (_, { entryCode }) => {
          const entryUrl = diUrlBase + entryCode;
          const isExpanded = expandedKeys.includes(entryUrl);
          const isHovered = hoveredKeys.includes(entryUrl);
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={entryUrl}
            />
          );
        },
      },
      {
        key: 'merchantOrderId',
        dataIndex: 'merchantOrderId',
        title: <Txt>{t('merchantOrderId')}</Txt>,
        align: 'center',
        render: (_, { merchantOrderId }) => {
          const isExpanded = expandedKeys.includes(merchantOrderId || '');
          const isHovered = hoveredKeys.includes(merchantOrderId || '');
          return (
            <TxtCompressible
              {...{ isWhite, isHovered, isExpanded }}
              setIsHover={setHoveredKeys}
              setIsExpanded={setExpandedKeys}
              text={merchantOrderId}
            />
          );
        },
      },
      {
        key: 'payerBankAccountName',
        dataIndex: 'payerBankAccountName',
        title: <Txt>{t('payerBankAccountName')}</Txt>,
        align: 'center',
        render: (_, { payerBankAccountName }) => <Txt>{payerBankAccountName}</Txt>,
      },
      {
        title: <Txt>{t('transactionType')}</Txt>,
        key: 'transactionType',
        align: 'center',
        render: ({ transactionType }) => {
          return <TagDiOrderType type={transactionType} />;
        },
      },
      {
        title: <Txt>{t('fiatInfo')}</Txt>,
        key: 'fiatInfo',
        align: 'center',
        render: (_, { fiatType, fiatAmount }) => (
          <main className='flex flex-col items-center'>
            <Txt>{fiatType}</Txt>
            <Txt type='secondary'>{fiatAmount}</Txt>
          </main>
        ),
      },
      {
        title: <Txt>{t('cryptoType')}</Txt>,
        key: 'cryptoType',
        align: 'center',
        render: (_, { cryptoType }) => {
          const cryptoOption = cryptoEnumOptions.find((option) => option.value === cryptoType);
          return (
            <main className='flex flex-col items-center'>
              <Space>
                <Avatar
                  size='small'
                  src={usdt}
                />
                <Txt>{cryptoOption?.label || cryptoType}</Txt>
              </Space>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('cryptoDetails')}</Txt>,
        key: 'cryptoDetails',
        align: 'center',
        render: (_, { cryptoAmount, totalFee }) => {
          const transferAmount = cryptoAmount - totalFee;

          return (
            <main className='flex flex-col items-center'>
              <div className='flex flex-col text-left'>
                <Space>
                  <Txt type='secondary'>{t('requireAmount')}:</Txt>
                  <Txt>{nTot({ value: cryptoAmount, digitsType: 'USDT' })}</Txt>
                </Space>
                <Space>
                  <Txt type='secondary'>{t('totalFee')}:</Txt>
                  <Txt>{nTot({ value: totalFee, digitsType: 'USDT' })}</Txt>
                </Space>
                <Space>
                  <Txt type='secondary'>{t('transferAmount')}:</Txt>
                  <Txt>{nTot({ value: transferAmount, digitsType: 'USDT' })}</Txt>
                </Space>
              </div>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('time')}</Txt>,
        key: 'time',
        align: 'center',
        render: (_, { createdAt, updatedAt }) => {
          const formattedDate = dayjs(createdAt);
          const formattedDateUpdated = dayjs(updatedAt);

          return (
            <main className='flex flex-col items-center'>
              <Space>
                <Txt type='secondary'>{t('createdAt')}:</Txt>
                <Txt>
                  {formattedDate.format('YYYY-MM-DD')} {formattedDate.format('HH:mm:ss')}
                </Txt>
              </Space>
              <Space>
                <Txt type='secondary'>{t('updatedAt')}:</Txt>
                <Txt>
                  {formattedDateUpdated.format('YYYY-MM-DD')} {formattedDateUpdated.format('HH:mm:ss')}
                </Txt>
              </Space>
            </main>
          );
        },
      },
      {
        title: <Txt>{t('merchantNotified')}</Txt>,
        key: 'isMerchantNotified',
        align: 'center',
        render: (_, { isMerchantNotified }) => (
          <Txt type={isMerchantNotified ? 'success' : 'danger'}>
            {isMerchantNotified ? t('notified') : t('notNotified')}
          </Txt>
        ),
      },
      {
        title: <Txt>{t('status')}</Txt>,
        key: 'status',
        align: 'center',
        render: (_, item) => {
          return <TagDIOrderStatus status={item.status} />;
        },
      },
    ];

    return result;
  }, [expandedKeys, hoveredKeys, isWhite, t]);

  return { columns };
};

export default useDiOrderColumns;
