import * as React from 'react';
import { useCallback, useMemo, useState } from 'react';
import { Breadcrumb, Flex, Select } from 'antd';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { DiOrderOptions, useDiOrderList } from '@/api';
import SearchMaster from '@/components/SearchMaster';
import TableAlpha from '@/components/TableAlpha';
import DateRange, { DateRangeOptions } from '@/components/DateRange';
import { dateFormator, useTableStates, useTranslateExcelTitleRow } from '@/hook';
import {
  cryptoEnumOptions,
  diOrderTypeEnumOptions,
  exportSheetByArray,
  ProxyOrderStatusEnum,
  proxyOrderStatusOptions,
  storageHelper,
} from '@/utils';
import { Txt } from '@/components/TypographyMaster';
import BtnFuncs from '@/components/BtnFuncs';
import useDiOrderColumns from './useDiOrderColumns';

interface IDiOrderProps {
  isActive?: boolean;
}

const diUrlBase = import.meta.env.VITE_DI_URL || 'https://k28.uxm-pay.uk/#/external/';

const DiOrder: React.FunctionComponent<IDiOrderProps> = (props) => {
  // props
  const {} = props || {};

  const storageRange = storageHelper<{ from: string; to: string }>('diOrderRange').getItem();
  const { t } = useTranslation('diOrder');
  const { t: columnsT } = useTranslation('useDiOrderColumns');
  const { t: optionsT } = useTranslation('options');
  const { translateExcelTitleRow } = useTranslateExcelTitleRow({ translator: 'diOrder' });
  const [, setDiOrderMatchFrom] = useState<DiOrderOptions>();
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({});
  const defaultDateRange = storageRange
    ? { from: dayjs(storageRange.from), to: dayjs(storageRange.to) }
    : { from: dayjs().startOf('d'), to: dayjs().endOf('d') };
  const [OrderUid, setOrderUid] = useState<string>();
  const [dateRange, setDateRange] = useState<DateRangeOptions>(defaultDateRange);
  const [MerchantOrderId, setMerchantOrderId] = useState<string>();
  const [statuses, setStatuses] = useState<Array<ProxyOrderStatusEnum>>([
    ProxyOrderStatusEnum.Cancelled,
    ProxyOrderStatusEnum.Completed,
    ProxyOrderStatusEnum.Created,
  ]);
  const translateProxyOrderStatusOptions = useMemo(() => {
    return proxyOrderStatusOptions
      .map((option) => ({
        ...option,
        label: optionsT(option.label),
      }))
      .filter((filterO) => filterO.value !== ProxyOrderStatusEnum.Expired);
  }, [optionsT]);

  const { data, isPending, isRefetching } = useDiOrderList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      OrderByDescending: true,
      OrderUid,
      MerchantOrderId,
      CreatedAtStart: dayjs(dateRange.from),
      CreatedAtEnd: dayjs(dateRange.to),
      Statuses: statuses,
    },
  });

  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  const { columns } = useDiOrderColumns({ data, setDiOrderMatchFrom });

  const handleOnDateSubmit = useCallback((newDate: DateRangeOptions) => {
    setDateRange(newDate);
    storageHelper<DateRangeOptions>('diOrderRange').setItem(newDate);
  }, []);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex className='my-5 flex flex-wrap justify-between gap-2'>
        <Flex
          gap={10}
          className='w-full'
          justify='space-between'
        >
          <DateRange
            loading={isRefetching}
            onDateSubmit={handleOnDateSubmit}
            defaultValues={dateRange}
          />
          <BtnFuncs
            iconType='print'
            loading={isRefetching}
            onClick={() => {
              const sheetTitleRow = [
                'orderUidTitleRow',
                'redirectUrlTitleRow',
                'merchantOrderIdTitleRow',
                'memberIdTitleRow',
                'payerBankAccountNameTitleRow',
                'entryCodeTitleRow',
                'transactionTypeTitleRow',
                'cryptoTypeTitleRow',
                'cryptoAmountTitleRow',
                'fiatTypeTitleRow',
                'fiatAmountTitleRow',
                'createdAtTitleRow',
                'merchantNotifiedTitleRow',
                'statusTitleRow',
              ];
              const sheetDataRows = [
                ...dataSource.map((diOrder) => {
                  const transactionType = diOrderTypeEnumOptions.find(
                    (findO) => findO.value === diOrder.transactionType,
                  );
                  const cryptoType = cryptoEnumOptions.find((findO) => findO.value === diOrder.cryptoType);
                  const statusOption = proxyOrderStatusOptions.find((findO) => findO.value === diOrder.status);
                  const createdAt = diOrder.createdAt ? dayjs(diOrder.createdAt).format(dateFormator.accurate) : '';
                  return [
                    diOrder.orderUid,
                    diUrlBase + diOrder.entryCode,
                    diOrder.merchantOrderId,
                    diOrder.memberId,
                    diOrder.payerBankAccountName,
                    diOrder.entryCode,
                    optionsT(transactionType?.label || 'undefined'),
                    cryptoType?.label || '',
                    diOrder.cryptoAmount,
                    diOrder.fiatType,
                    diOrder.fiatAmount,
                    createdAt,
                    diOrder.isMerchantNotified ? columnsT('notified') : columnsT('notNotified'),
                    optionsT(statusOption?.label || 'undefined'),
                  ];
                }),
              ];

              exportSheetByArray({
                arrays: [translateExcelTitleRow(sheetTitleRow), ...sheetDataRows],
                sheetName: t('fileName'),
                fileName: `${t('fileName')} ${dateRange.from.format(dateFormator.accurate)} ~ ${dateRange.to.format(
                  dateFormator.accurate,
                )} `,
              });
            }}
          />
        </Flex>
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='orderUid'
        titleRender={
          <div className='items- flex flex-wrap items-center gap-x-2'>
            <div className='flex flex-col gap-y-1'>
              <Txt
                type='secondary'
                className='font-bold'
              >
                {t('statusLabel')}:
              </Txt>
              <Select
                placeholder={t('statusPlaceholder')}
                variant='filled'
                className='w-[160px]'
                options={translateProxyOrderStatusOptions}
                disabled={isRefetching}
                onChange={(newValue) => setStatuses(newValue)}
                allowClear
                mode='multiple'
              />
            </div>
            <SearchMaster
              titles={[
                { key: 'OrderUid', label: t('orderUidLabel') },
                { key: 'MerchantOrderId', label: t('merchantOrderIdLabel') },
              ]}
              onSearch={(values) => {
                setOrderUid(values.OrderUid || undefined);
                setMerchantOrderId(values.MerchantOrderId || undefined);
              }}
            />
          </div>
        }
      />
    </div>
  );
};

export default DiOrder;
