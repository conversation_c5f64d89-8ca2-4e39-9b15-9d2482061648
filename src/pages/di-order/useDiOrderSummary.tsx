// libs
import { Table, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';

// api
import { DiOrderOptions } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

type DiOrderSummaryProps = {
  columns: TableProps<DiOrderOptions>['columns'];
};

const useDiOrderSummary = (props: DiOrderSummaryProps) => {
  // props
  const { columns } = props || {};

  // hooks
  const { t } = useTranslation('useDiOrderColumns');

  // compute
  const summary = useCallback(
    (pageData: readonly DiOrderOptions[]) => {
      const summaryData = pageData;
      
      // Calculate totals for crypto details
      const totalRequireAmount = summaryData.reduce((preValue, current) => preValue + current.cryptoAmount, 0);
      const totalFee = summaryData.reduce((preValue, current) => preValue + current.totalFee, 0);
      const totalTransferAmount = totalRequireAmount - totalFee;

      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns?.map((mapC, index) => {
              const key = `${mapC.key}-${index}`;

              // Show subtotal label in the fiatInfo column (before cryptoDetails)
              if (mapC.key === 'fiatInfo')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center font-bold'
                  >
                    <Txt>{t('subtotal')}</Txt>
                  </Table.Summary.Cell>
                );

              // Show the crypto details summary
              if (mapC.key === 'cryptoDetails')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-center'
                  >
                    <main className='flex flex-col items-center'>
                      <div className='flex flex-col text-left'>
                        <div className='flex justify-between gap-2'>
                          <Txt type='secondary'>{t('requireAmount')}:</Txt>
                          <Txt className='font-medium'>{nTot({ value: totalRequireAmount, digitsType: 'USDT' })}</Txt>
                        </div>
                        <div className='flex justify-between gap-2'>
                          <Txt type='secondary'>{t('totalFee')}:</Txt>
                          <Txt className='font-medium'>{nTot({ value: totalFee, digitsType: 'USDT' })}</Txt>
                        </div>
                        <div className='flex justify-between gap-2'>
                          <Txt type='secondary'>{t('transferAmount')}:</Txt>
                          <Txt className='font-medium'>{nTot({ value: totalTransferAmount, digitsType: 'USDT' })}</Txt>
                        </div>
                      </div>
                    </main>
                  </Table.Summary.Cell>
                );

              // Empty cells for other columns
              return (
                <Table.Summary.Cell
                  key={key}
                  index={index}
                />
              );
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns, t],
  );

  return summary;
};

export default useDiOrderSummary;
