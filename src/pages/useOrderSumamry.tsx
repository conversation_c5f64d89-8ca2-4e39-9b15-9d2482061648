// libs
import { Table, TableProps } from 'antd';
import { useTranslation } from 'react-i18next';
import Decimal from 'decimal.js';
import { useCallback } from 'react';

// api
import { OderListMerchantOptions } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// utils
import { nTot } from '@/utils';

type OrderSumamryProps = {
  columns: TableProps<OderListMerchantOptions>['columns'];
};
const useOrderSumamry = (props: OrderSumamryProps) => {
  // props
  const { columns } = props || {};

  // hooks
  const { t } = useTranslation('useOrderSummary');

  // compute
  const summary = useCallback(
    (pageData: readonly OderListMerchantOptions[]) => {
      const summrayData = pageData;
      const totalRequireAmount = summrayData.reduce((preValue, current) => preValue + current.order.requireAmount, 0);
      const totalActualAmount = summrayData.reduce((preValue, current) => preValue + current.order.actualAmount, 0);
      // use Decimal correctly calculated
      let totalGas = new Decimal(0);
      summrayData.forEach((item) => {
        totalGas = totalGas.plus(item.order.gas);
      });
      const totalFee = summrayData.reduce((preValue, current) => preValue + current.order.fee, 0);
      return (
        <Table.Summary fixed>
          <Table.Summary.Row>
            {columns?.map((mapC, index) => {
              const key = `${mapC.key}-${index}`;

              if (mapC.key === 'merchantUserName')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-right font-bold'
                  >
                    <Txt>{t('subtotal')}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'requireAmount')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-right'
                  >
                    <Txt className='pr-4'> {nTot({ value: totalRequireAmount })}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'actualAmount')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-right'
                  >
                    <Txt className='pr-4'> {nTot({ value: totalActualAmount })}</Txt>
                  </Table.Summary.Cell>
                );
              if (mapC.key === 'gas')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-right'
                  >
                    <Txt className='pr-4'> {nTot({ value: totalGas.toString() })}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'fee')
                return (
                  <Table.Summary.Cell
                    key={key}
                    index={index}
                    className='text-right'
                  >
                    <Txt className='pr-4'> {nTot({ value: totalFee })}</Txt>
                  </Table.Summary.Cell>
                );

              if (mapC.key === 'state') return undefined;
              return (
                <Table.Summary.Cell
                  key={key}
                  index={index}
                />
              );
            })}
          </Table.Summary.Row>
        </Table.Summary>
      );
    },
    [columns, t],
  );

  return summary;
};

export default useOrderSumamry;
