// libs
import { PlusOutlined } from '@ant-design/icons';
import { Flex, Button, Breadcrumb, Space } from 'antd';
import * as React from 'react';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

// apis
import { McUserListOptions, useMcUserList } from '@/api';

// components
import BindingGA from '@/components/BindingGA';
import TableAlpha from '@/components/TableAlpha';
import SearchMaster from '@/components/SearchMaster';
import BtnFuncs from '@/components/BtnFuncs';

// hooks
import { dateFormator, useTableStates } from '@/hook';

// utils
import { exportSheetByArray, arrayObjToArraySheet } from '@/utils';

// pages
import SubAccountDetail from './components/SubAccountDetail';
import SubAccountCreate from './components/SubAccountCreate';
import useSubAccountColumns from './useSubAccountColumns';

interface ISubAccountProps {}

const SubAccount: React.FunctionComponent<ISubAccountProps> = (props) => {
  // props
  const {} = props || {};

  // hooks
  const { t } = useTranslation('subAccount');
  const { pageSize, setPageSize, currentPage, setCurrentPage } = useTableStates({ sizeInit: 15 });

  // states
  const [openSubAccountDetail, setOpenSubAccountDetail] = useState<McUserListOptions>();
  const [openSubAccountCreate, setOpenSubAccountCreate] = useState(false);
  const [openBindingGA, setOpenBindingGA] = useState(false);
  const [AppUserId, setAppUserId] = useState<string>();
  const [UserName, setUserName] = useState<string>();
  const [NickName, setNickName] = useState<string>();

  const { data, isPending, isRefetching } = useMcUserList({
    params: {
      PageNumber: currentPage,
      PageSize: pageSize,
      AppUserId,
      UserName,
      NickName,
    },
  });

  const { columns } = useSubAccountColumns({ data, setOpenSubAccountDetail });
  const dataSource = useMemo(() => {
    return Array.isArray(data?.items) ? data.items : [];
  }, [data]);

  return (
    <div>
      <Breadcrumb
        items={[
          {
            title: <Link to='private'>{t('breadcrumbHome')}</Link>,
          },
          {
            title: t('breadcrumbCurrent'),
          },
        ]}
      />
      <Flex
        justify='space-between'
        className='my-5'
      >
        <Space>
          <Button
            icon={<PlusOutlined />}
            loading={isRefetching}
            shape='round'
            type='primary'
            onClick={() => setOpenSubAccountCreate(true)}
          >
            {t('createButton')}
          </Button>
        </Space>
        <BtnFuncs
          iconType='print'
          loading={isRefetching}
          onClick={() => {
            const currentTime = dayjs().format('YYYY-MM-DD');
            exportSheetByArray({
              arrays: arrayObjToArraySheet(dataSource.map((mapData) => ({ ...mapData }))),
              sheetName: `${currentTime}`,
              fileName: `${t('fileName')} ${dayjs().format(dateFormator.clock)}`,
            });
          }}
        />
      </Flex>
      <TableAlpha
        {...{ dataSource, columns, pageSize, setPageSize, currentPage, setCurrentPage }}
        loading={isPending || isRefetching}
        totalDataLength={data?.totalCount}
        size='small'
        rowKey='appUserId'
        titleRender={
          <SearchMaster
            titles={[
              { key: 'AppUserId', label: t('appUserIdLabel') },
              { key: 'UserName', label: t('usernameLabel') },
              { key: 'NickName', label: t('nickNameLabel') },
            ]}
            onSearch={(values) => {
              setAppUserId(values.AppUserId || undefined);
              setUserName(values.UserName || undefined);
              setNickName(values.NickName || undefined);
            }}
          />
        }
      />
      <SubAccountDetail
        user={openSubAccountDetail}
        setUser={setOpenSubAccountDetail}
      />
      <SubAccountCreate
        open={openSubAccountCreate}
        onCancel={() => setOpenSubAccountCreate(false)}
      />
      <BindingGA
        open={openBindingGA}
        onCancel={() => setOpenBindingGA(false)}
      />
    </div>
  );
};

export default SubAccount;
