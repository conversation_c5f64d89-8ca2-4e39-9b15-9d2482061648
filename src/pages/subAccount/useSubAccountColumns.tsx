// libs
import { useCallback, useMemo } from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CrownOutlined,
  LockOutlined,
  UnlockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Space, TableColumnsType, Tag } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

// apis
import { McUserListOptions, McUserListRes } from '@/api';

// components
import { Txt } from '@/components/TypographyMaster';

// hooks
import { dateFormator } from '@/hook';

type SubAccountDetail = {
  data: McUserListRes | undefined;
  setOpenSubAccountDetail: React.Dispatch<React.SetStateAction<McUserListOptions | undefined>>;
};

const useSubAccountColumns = (useProps: SubAccountDetail) => {
  // props
  const { data, setOpenSubAccountDetail } = useProps;

  // hooks
  const { t } = useTranslation('useSubAccountColumns');

  // compute
  const OnclickMcUserDetail = useCallback(
    (row: McUserListOptions) => {
      setOpenSubAccountDetail(data?.items.find((findI) => findI.id === row.id));
    },
    [setOpenSubAccountDetail, data?.items],
  );

  const columns = useMemo(() => {
    const result: TableColumnsType<McUserListOptions> = [
      {
        title: () => <Txt>{t('username')}</Txt>,
        key: 'userName',
        align: 'center',
        dataIndex: 'userName',
      },
      {
        title: () => <Txt>{t('nickName')}</Txt>,
        key: 'nickName',
        align: 'center',
        dataIndex: 'nickName',
      },
      {
        title: () => <Txt>{t('permissions')}</Txt>,
        key: 'roles',
        align: 'left',
        render: ({ roles }) => {
          const roleDisplay = roles[0] === 'MerchantAdmin' ? 'admin' : 'user';
          const icon = roleDisplay === 'admin' ? <CrownOutlined /> : <UserOutlined />;
          return (
            <Space size='small'>
              {icon}
              <Txt>{roleDisplay === 'admin' ? 'Admin' : 'User'}</Txt>
            </Space>
          );
        },
      },
      {
        title: () => <Txt>{t('twoFactorEnabled')}</Txt>,
        dataIndex: 'twoFactorEnabled',
        key: 'twoFactorEnabled',
        align: 'center',
        render: (twoFactorEnabled: boolean) => {
          return twoFactorEnabled ? (
            <Tag
              icon={<CheckCircleOutlined />}
              color='success'
            >
              {t('activeTag')}
            </Tag>
          ) : (
            <Tag
              icon={<CloseCircleOutlined />}
              color='default'
            >
              {t('deactiveTag')}
            </Tag>
          );
        },
      },
      {
        title: () => <Txt>{t('operationPassword')}</Txt>,
        dataIndex: 'operationPasswordEnabled',
        key: 'operationPasswordEnabled',
        align: 'center',
        render: (operationPasswordEnabled: boolean) => {
          return operationPasswordEnabled ? (
            <Tag
              icon={<CheckCircleOutlined />}
              color='success'
            >
              {t('activeTag')}
            </Tag>
          ) : (
            <Tag
              icon={<CloseCircleOutlined />}
              color='default'
            >
              {t('deactiveTag')}
            </Tag>
          );
        },
      },
      {
        title: () => <Txt>{t('isLockedOut')}</Txt>,
        dataIndex: 'isLockedOut',
        key: 'isLockedOut',
        align: 'center',
        render: (isLockedOut: boolean) => {
          return isLockedOut ? (
            <Tag
              icon={<LockOutlined />}
              color='success'
            >
              {t('activeTag')}
            </Tag>
          ) : (
            <Tag
              icon={<UnlockOutlined />}
              color='default'
            >
              {t('deactiveTag')}
            </Tag>
          );
        },
      },
      {
        title: () => <Txt>{t('createdAt')}</Txt>,
        dataIndex: 'createdAt',
        key: 'createdAt',
        align: 'center',
        render: (createdAt: string) => {
          return <Txt>{dayjs(createdAt).format(dateFormator.accurate)} </Txt>;
        },
      },
      {
        title: <Txt>{t('operationPassword')}</Txt>,
        key: 'operate',
        align: 'center',
        render: (row: McUserListOptions) => (
          <div>
            <Button
              color='default'
              onClick={() => OnclickMcUserDetail(row)}
            >
              {t('detail')}
            </Button>
          </div>
        ),
      },
    ];

    return result;
  }, [OnclickMcUserDetail, t]);

  return { columns };
};

export default useSubAccountColumns;
