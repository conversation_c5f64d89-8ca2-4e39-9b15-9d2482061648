// libs
import * as React from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { useTranslation } from 'react-i18next';

// api
import { useCreateMerchant } from '@/api';

// utils
import validatePassword from '@/utils';

// store
import { useNotifyStore } from '@/store';

interface ISubAccountCreateProps {
  open: boolean;
  onCancel: () => void;
}

const SubAccountCreate: React.FunctionComponent<ISubAccountCreateProps> = ({ open, onCancel }) => {
  // states
  const [merchantId] = React.useState<number>(1);

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { t } = useTranslation('subAccountCreate');

  const { mutate: setCAOder, isPending: inLoad } = useCreateMerchant({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);

      onCancel();
      form.resetFields();
    },
    onError: () => {
      form.resetFields();
    },
  });

  return (
    <Modal
      title={t('title')}
      footer={false}
      open={open}
      onCancel={onCancel}
    >
      <Form
        form={form}
        name='createForm'
        onFinish={(values) => {
          const payload = {
            ...values,
            merchantId,
          };
          setCAOder(payload);
        }}
      >
        <Form.Item
          label={t('usernameLabel')}
          name='userName'
          rules={[{ required: true, message: t('usernameErrorMessage') }]}
        >
          <Input
            placeholder={t('usernamePlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item
          label={t('nickNameLabel')}
          name='nickName'
          rules={[{ required: true, message: t('nickNameErrorMessage') }]}
        >
          <Input
            placeholder={t('nickNamePlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item
          label={t('passwordLabel')}
          name='password'
          rules={[{ validator: validatePassword }]}
        >
          <Input.Password
            placeholder={t('passwordPlaceholder')}
            disabled={inLoad}
          />
        </Form.Item>
        <Form.Item>
          <Button
            block
            type='primary'
            htmlType='submit'
            loading={inLoad}
          >
            {t('submit')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SubAccountCreate;
