// libs
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserDeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import { Button, Descriptions, Form, Modal, Tag, Tooltip } from 'antd';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';

// api
import { McUserListOptions, useBlockMerchant, useDeLMerchant } from '@/api';

// components
import MainAccountVertify from '@/components/MainAccountVertify';
import { Txt } from '@/components/TypographyMaster';
import BtnFuncs from '@/components/BtnFuncs';

// hooks
import { dateFormator } from '@/hook';

// utils
import { logInfo } from '@/utils';

// store
import { useNotifyStore } from '@/store';
import { useThemeStore } from '@/store/useThemeStore';

interface SubAccountDetailProps {
  user: McUserListOptions | undefined;
  setUser: React.Dispatch<React.SetStateAction<McUserListOptions | undefined>>;
}

const SubAccountDetail: React.FunctionComponent<SubAccountDetailProps> = (props) => {
  // props
  const { user, setUser } = props || {};

  // states
  const [openMainAccountVertify, setOpenMainAccountVertify] = useState(false);
  const [actionType, setActionType] = useState<'delete' | 'block' | null>(null); // Thêm state cho hành động

  // hooks
  const [form] = Form.useForm();
  const { pushBSQ } = useNotifyStore();
  const { isWhite } = useThemeStore();
  const { t } = useTranslation('subAccountDetail');

  const { mutate: delMC, isPending: isD } = useDeLMerchant({
    onSuccess: () => {
      pushBSQ([{ title: 'UXM Client', des: t('successDescription') }]);
      setUser(undefined);
      form.resetFields();
      setOpenMainAccountVertify(false);
    },
  });

  const { mutate: bloMC, isPending: isB } = useBlockMerchant({
    onSuccess: () => {
      const message = user?.isLockedOut
        ? { title: 'UXM Client', des: t('unlockSuccessDescription') } // Unlock
        : { title: 'UXM Client', des: t('blockSuccessDescription') }; // Block

      pushBSQ([message]);
      setUser(undefined);
      form.resetFields();
      setOpenMainAccountVertify(false);
    },
    onError: () => {
      setOpenMainAccountVertify(false);
    },
  });

  // handlers
  const handleDeleteMC = useCallback(() => {
    if (!user?.appUserId) {
      return;
    }
    setActionType('delete');
    logInfo('check deleteOpenModal');
    setOpenMainAccountVertify(true);
  }, [user]);

  const handleBlockMC = useCallback(() => {
    if (!user?.appUserId) {
      return;
    }
    setActionType('block');
    logInfo('check blockOpenModal');
    setOpenMainAccountVertify(true);
  }, [user]);

  const handleAction = (values: { operationPassword: string }) => {
    if (!user?.appUserId) return;

    if (actionType === 'delete') {
      delMC({ userId: user.appUserId, operationPassword: values.operationPassword });
    } else if (actionType === 'block') {
      bloMC({ userId: user.appUserId, operationPassword: values.operationPassword });
    }
  };

  const items = [
    {
      key: '1',
      label: <Txt>{t('username')}</Txt>,
      children: <div className='text-lg font-bold'>{user?.userName}</div>,
    },
    {
      key: '2',
      label: <Txt>{t('nickName')}</Txt>,
      children: <Txt>{user?.nickName}</Txt>,
    },
    {
      key: '3',
      label: <Txt>{t('merchantNumber')}</Txt>,
      children: <Txt>{user?.merchantNumber}</Txt>,
    },
    {
      key: '4',
      label: <Txt>{t('merchantName')}</Txt>,
      children: <Txt>{user?.merchantName}</Txt>,
    },
    {
      key: '5',
      label: <Txt>{t('userId')}</Txt>,
      children: <Txt copyable>{user?.appUserId}</Txt>,
    },
    {
      key: '6',
      label: <Txt>{t('operationPassword')}</Txt>,
      children: user?.operationPasswordEnabled ? (
        <Tag
          icon={<CheckCircleOutlined />}
          className='cursor-pointer'
          color='success'
        >
          {t('activeTag')}
        </Tag>
      ) : (
        <Tag
          icon={<CloseCircleOutlined />}
          color='default'
          className='cursor-pointer'
        >
          {t('deactiveTag')}
        </Tag>
      ),
    },
    {
      key: '7',
      label: <Txt>{t('permissions')}</Txt>,
      children: <Txt>{user?.roles[0]}</Txt>,
    },
    {
      key: '8',
      label: <Txt>{t('gaVerification')}</Txt>,
      children: user?.twoFactorEnabled ? (
        <Tag
          icon={<CheckCircleOutlined />}
          className='cursor-pointer'
          color='success'
        >
          {t('activeTag')}
        </Tag>
      ) : (
        <Tag
          icon={<CloseCircleOutlined />}
          color='default'
          className='cursor-pointer'
        >
          {t('deactiveTag')}
        </Tag>
      ),
    },
    {
      key: '9',
      label: <Txt>{t('createdAt')}</Txt>,
      children: dayjs(user?.createdAt).format(dateFormator.accurate),
    },
    {
      key: '10',
      label: <Txt>{t('lockedOut')}</Txt>,
      children: user?.isLockedOut ? (
        <Tag
          icon={<LockOutlined />}
          className='cursor-pointer'
          color='success'
        >
          {t('activeTag')}
        </Tag>
      ) : (
        <Tag
          icon={<UnlockOutlined />}
          color='default'
          className='cursor-pointer'
        >
          {t('deactiveTag')}
        </Tag>
      ),
    },
  ];

  return (
    <div>
      <Modal
        title={t('title')}
        footer={false}
        open={!!user}
        onCancel={() => setUser(undefined)}
        width='fit-content'
      >
        <Descriptions
          size='small'
          bordered
          items={items}
          column={2}
          className='mb-8'
        />
        <div className='flex justify-between'>
          <Tooltip title={t('deleteTooltip')}>
            <BtnFuncs
              iconType='remove'
              type='text'
              onClick={handleDeleteMC}
              danger
              disabled={isD}
            />
          </Tooltip>
          <Button
            icon={user?.isLockedOut ? <UserDeleteOutlined /> : <UserAddOutlined />}
            type='default'
            onClick={handleBlockMC}
            disabled={isB || isD}
            className={`flex items-center space-x-2 ${
              isWhite ? 'bg-yellow-500' : 'text-yellow-400'
            } rounded-lg px-4 py-2 transition-all duration-200 ease-in-out`}
          >
            {user?.isLockedOut ? t('unblockButton') : t('blockButton')}
          </Button>
        </div>
      </Modal>

      <MainAccountVertify
        open={openMainAccountVertify}
        onCancel={() => setOpenMainAccountVertify(false)}
        onSubmit={handleAction}
        loading={isD || isB}
        form={form}
      />
    </div>
  );
};

export default SubAccountDetail;
