import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import ErrorBoundary from 'antd/es/alert/ErrorBoundary';
import { ConfigProvider, theme } from 'antd';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AppRouter from './routes/AppRouter';
import FallbackLoading from './components/FallbackLoading';
import NotifyProvider from './components/NotifyProvider';
import Mask from './components/Mask';
import SpinMaster from './components/SpinMaster';
import { useUserStore, useNotifyStore } from './store';
import { useThemeStore } from './store/useThemeStore';
import './i18next';

const version = import.meta.env.VITE_VERSION;
// eslint-disable-next-line no-console
if (import.meta.env.PROD) console.log(`App version: ${version}`);

const App: React.FunctionComponent = () => {
  // store
  const { basicErrorQue, shiftBEQ, basicSuccessQue, shiftBSQ } = useNotifyStore();
  const { isLoading } = useUserStore();
  // theme
  const { isWhite } = useThemeStore();
  const algorithm = isWhite ? theme.defaultAlgorithm : theme.darkAlgorithm;

  return (
    <>
      <ConfigProvider
        theme={{
          algorithm,
          token: { colorPrimary: '#52c41a' },
          components: {
            Layout: {
              bodyBg: isWhite ? '#f0f0f0' : '#303233',
              siderBg: '#1f2326',
              headerBg: isWhite ? '#fff' : '#1f2326',
              headerPadding: '0 16px',
              triggerBg: '#101519',
            },
            Menu: {
              darkItemBg: '#1f2326',
            },
            Card: {
              colorBgContainer: isWhite ? '#fff' : '#232526',
            },
            Tabs: {
              titleFontSizeLG: 80,
            },
          },
        }}
      >
        <Suspense fallback={<FallbackLoading />}>
          <AppRouter />
        </Suspense>
      </ConfigProvider>

      {/* Dialogues */}
      <Mask open={isLoading}>
        <SpinMaster />
      </Mask>
      <NotifyProvider
        basicQue={basicErrorQue}
        type='error'
        shiftBsicQue={shiftBEQ}
      />
      <NotifyProvider
        basicQue={basicSuccessQue}
        shiftBsicQue={shiftBSQ}
        type='success'
      />

      <ReactQueryDevtools />
    </>
  );
};

const Main: React.FunctionComponent = () => {
  const queryClient = new QueryClient();

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Main />
  </React.StrictMode>,
);
